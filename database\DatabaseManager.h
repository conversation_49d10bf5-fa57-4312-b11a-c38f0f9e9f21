#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <sqlite3.h>
#include "TaskData.h"
#include "../utils/Logger.h"
#include "../utils/Exceptions.h"

class DatabaseManager {
public:
    static DatabaseManager& getInstance();

    bool initialize(const std::string& dbPath);
    void cleanup();

    // 任务数据操作
    bool insertTaskData(const TaskData& data);
    std::vector<TaskData> getLatestTaskData(int limit = 100);
    std::vector<TaskData> getTaskDataByTaskId(const std::string& taskId, const std::string& dataType = "");
    TaskData getTaskResultByTaskId(const std::string& taskId);

    // 新增查询方法（用于后续报告生成）
    std::vector<TaskData> getResultsByDllAndFunc(const std::string& dllName, const std::string& funcName, int limit = 100);

    // 数据清理
    bool cleanupOldData(int daysToKeep = 30);

    // 批量插入
    bool batchInsertTaskData(const std::vector<TaskData>& dataList);

    // 获取最后一次错误
    std::string getLastError() const { return lastError_; }

    // 获取数据库句柄（供JsonSchemaProcessor使用）
    sqlite3* getDbHandle() const { return m_db; }

    // 执行SQL查询（供JsonSchemaProcessor使用）
    bool executeQuery(const std::string& sql);

private:
    DatabaseManager() = default;
    ~DatabaseManager() = default;
    DatabaseManager(const DatabaseManager&) = delete;
    DatabaseManager& operator=(const DatabaseManager&) = delete;

    bool createTables();
    std::string getCurrentTimestamp();

    // 预编译语句
    sqlite3_stmt* prepareStatement(const std::string& sql);

    sqlite3* m_db = nullptr;
    std::string lastError_;

    // 预编译语句缓存
    std::map<std::string, sqlite3_stmt*> preparedStatements_;
};