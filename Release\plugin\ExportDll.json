﻿{
  "name": "ExportDll",
  "version": "1.0.0",
  "description": "导出函数",
  "functions": [
    {
      "name": "ProcessBase64ToXlsx",
      "signature": "standard"
    },
    {
      "name": "ProcessBase64ToFile",
      "signature": "standard"
    },
    {
      "name": "DecryptAndStoreToDatabaseWithCompany",
      "signature": "standard"
    },
    {
      "name": "QueryGlobalConfig",
      "signature": "standard"
    }
  ]
}
