﻿#include "WebSocketServer.h"
#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
#include "../utils/Logger.h"
#include "../utils/Utils.h"

//namespace asio = websocketpp::lib::asio;

WebSocketServerImpl::WebSocketServerImpl() : m_running(false) {
    // 初始化服务器
    m_server.clear_access_channels(websocketpp::log::alevel::all);
    m_server.init_asio();

    // 设置回调函数
    m_server.set_open_handler(std::bind(&WebSocketServerImpl::OnOpen, this, std::placeholders::_1));
    m_server.set_close_handler(std::bind(&WebSocketServerImpl::OnClose, this, std::placeholders::_1));
    m_server.set_message_handler(std::bind(&WebSocketServerImpl::OnMessage, this, std::placeholders::_1, std::placeholders::_2));
}

WebSocketServerImpl::~WebSocketServerImpl() {
    Stop();
}

bool WebSocketServerImpl::Start(int port) {
    try {
        // 使用正确的命名空间
        m_server.listen(asio::ip::tcp::v4(), port);
        m_server.start_accept();
        m_running = true;
        return true;
    }
    catch (const std::exception& e) {
        // 添加错误日志输出
        LogError("WebSocket启动失败: " + std::string(e.what()));
        return false;
    }
}

void WebSocketServerImpl::Stop() {
    if (!m_running) return;

    m_server.stop_listening();

    // 获取连接集合的副本
    std::set<ConnectionHandle, std::owner_less<ConnectionHandle>> connections_copy;
    {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        connections_copy = m_connections;
        m_connections.clear();
    }

    // 关闭所有连接
    for (auto& connection : connections_copy) {
        try {
            m_server.close(connection, websocketpp::close::status::normal, "Server shutting down");
        }
        catch (...) {}
    }

    m_running = false;
}

bool WebSocketServerImpl::SendTo(const ConnectionHandle& hdl, const std::string& message) {
    try {
        m_server.send(hdl, message, websocketpp::frame::opcode::text);
        return true;
    }
    catch (...) {
        return false;
    }
}

// 向所有客户端广播消息
void WebSocketServerImpl::Broadcast(const std::string& message) {
    // 记录日志
    LogInfo("广播消息: " + message);

    // 创建副本以避免同步问题
    std::set<ConnectionHandle, std::owner_less<ConnectionHandle>> connections_copy;
    {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        connections_copy = m_connections;
    }

    // 向所有连接的客户端发送消息
    for (auto& hdl : connections_copy) {
        try {
            m_server.send(hdl, message, websocketpp::frame::opcode::text);
        } catch (const std::exception& e) {
            LogError("广播消息失败: " + std::string(e.what()));
        }
    }
}

void WebSocketServerImpl::RegisterFunction(const std::string& name, LocalFunction func) {
    m_functions[name] = func;
}

void WebSocketServerImpl::OnOpen(ConnectionHandle hdl) {
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    m_connections.insert(hdl);
    LogInfo("客户端已连接，当前连接数: " + std::to_string(m_connections.size()));
}

void WebSocketServerImpl::OnClose(ConnectionHandle hdl) {
    std::lock_guard<std::mutex> lock(m_connectionsMutex);
    m_connections.erase(hdl);
    LogInfo("客户端已断开，当前连接数: " + std::to_string(m_connections.size()));
}

void WebSocketServerImpl::OnMessage(ConnectionHandle hdl, message_ptr msg) {
    try {
        // 解析JSON消息
        rapidjson::Document doc;
        doc.Parse(msg->get_payload().c_str());

        if (!doc.IsObject()) {
            SendTo(hdl, "{\"error\":\"Invalid message format: not a JSON object\"}");
            return;
        }

        // 检查消息类型
        std::string messageType;
        if (doc.HasMember("type") && doc["type"].IsString()) {
            messageType = doc["type"].GetString();
        }

        std::string result;

        // 处理新的统一执行请求
        if (messageType == "execute") {
            LogInfo("Received 'execute' request");

            // 新的统一API调用格式
            if (!doc.HasMember("function") || !doc["function"].IsString()) {
                std::string errorMsg = "Missing or invalid 'function' parameter";
                LogError(errorMsg);
                SendTo(hdl, "{\"error\":\"" + errorMsg + "\"}");
                return;
            }

            std::string functionId = doc["function"].GetString();
            LogInfo("Function ID: " + functionId);

            // 检查函数标识符格式
            if (functionId.find("::") == std::string::npos) {
                std::string errorMsg = "Invalid function identifier format, expected 'dllName::funcName'";
                LogError(errorMsg);
                SendTo(hdl, "{\"error\":\"" + errorMsg + "\"}");
                return;
            }

            // 提取DLL名称和函数名
            size_t separatorPos = functionId.find("::");
            std::string dllName = functionId.substr(0, separatorPos);
            std::string funcName = functionId.substr(separatorPos + 2);
            LogInfo("DLL name: " + dllName + ", Function name: " + funcName);

            // 提取参数
            std::string paramsJson;
            if (doc.HasMember("params")) {
                rapidjson::StringBuffer buffer;
                rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
                doc["params"].Accept(writer);
                paramsJson = buffer.GetString();
                LogInfo("Parameters: " + paramsJson);
            } else {
                paramsJson = "{}";
                LogInfo("No parameters provided, using empty object");
            }

            // 提取优先级
            int priority = 0;
            if (doc.HasMember("priority") && doc["priority"].IsInt()) {
                priority = doc["priority"].GetInt();
                LogInfo("Priority: " + std::to_string(priority));
            } else {
                LogInfo("No priority specified, using default (0)");
            }

            // 提交任务
            LogInfo("Submitting task to DLLRouter...");
            std::string taskId = DLLRouter::getInstance().submitTask(dllName, funcName, paramsJson, priority);
            LogInfo("Task submitted, ID: " + taskId);

            // 返回任务ID
            rapidjson::StringBuffer responseBuffer;
            rapidjson::Writer<rapidjson::StringBuffer> responseWriter(responseBuffer);
            responseWriter.StartObject();
            responseWriter.Key("type");
            responseWriter.String("execute_response");
            responseWriter.Key("taskId");
            responseWriter.String(taskId.c_str());
            responseWriter.Key("status");
            responseWriter.String("success");
            responseWriter.EndObject();

            std::string response = responseBuffer.GetString();
            LogInfo("Sending response: " + response);
            SendTo(hdl, response);
            return;
        }
        // 向后兼容的旧格式消息处理
        else if (doc.HasMember("func") && doc.HasMember("params")) {
            std::string funcName = doc["func"].GetString();
            std::string params = doc["params"].GetString();

            bool isDLLFunction = funcName.find("::") != std::string::npos;

            if (isDLLFunction) {
                // 调用DLL函数
                result = DLLRouter::getInstance().executeFunction(funcName, params);
                if (result.empty() && !DLLRouter::getInstance().getLastError().empty()) {
                    SendTo(hdl, "{\"error\":\"" + DLLRouter::getInstance().getLastError() + "\"}");
                    return;
                }
            }
            else {
                // 调用本地函数
                auto it = m_functions.find(funcName);
                if (it == m_functions.end()) {
                    SendTo(hdl, "{\"error\":\"Function not found\"}");
                    return;
                }
                result = it->second(params);
            }
        }
        else {
            SendTo(hdl, "{\"error\":\"Invalid message format: missing required fields\"}");
            return;
        }

        // 检查结果大小,如果超过1MB则分块发送
        const size_t MAX_CHUNK_SIZE = 1024 * 1024; // 1MB
        if (result.length() > MAX_CHUNK_SIZE) {
            // 先解析结果确保是有效的JSON
            rapidjson::Document resultDoc;
            resultDoc.Parse(result.c_str());
            if (resultDoc.HasParseError()) {
                SendTo(hdl, "{\"error\":\"Invalid JSON result\"}");
                return;
            }

            // 发送开始标记
            rapidjson::StringBuffer buffer;
            rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
            writer.StartObject();
            writer.Key("type");
            writer.String("chunk_start");
            writer.Key("total_size");
            writer.Uint64(result.length());
            writer.Key("content_type");
            writer.String("json");
            writer.EndObject();
            SendTo(hdl, buffer.GetString());

            // 使用Base64编码避免JSON截断问题
            std::string base64Result;
            base64Result.reserve(((result.length() + 2) / 3) * 4);
            static const char* base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

            size_t i = 0;
            for (; i + 2 < result.length(); i += 3) {
                base64Result.push_back(base64Chars[(result[i] & 0xfc) >> 2]);
                base64Result.push_back(base64Chars[((result[i] & 0x03) << 4) + ((result[i + 1] & 0xf0) >> 4)]);
                base64Result.push_back(base64Chars[((result[i + 1] & 0x0f) << 2) + ((result[i + 2] & 0xc0) >> 6)]);
                base64Result.push_back(base64Chars[result[i + 2] & 0x3f]);
            }

            if (i < result.length()) {
                size_t j = 0;
                base64Result.push_back(base64Chars[(result[i] & 0xfc) >> 2]);
                if (i + 1 < result.length()) {
                    base64Result.push_back(base64Chars[((result[i] & 0x03) << 4) + ((result[i + 1] & 0xf0) >> 4)]);
                    base64Result.push_back(base64Chars[(result[i + 1] & 0x0f) << 2]);
                    base64Result.push_back('=');
                } else {
                    base64Result.push_back(base64Chars[(result[i] & 0x03) << 4]);
                    base64Result.push_back('=');
                    base64Result.push_back('=');
                }
            }

            // 分块发送Base64编码后的数据
            for (size_t i = 0; i < base64Result.length(); i += MAX_CHUNK_SIZE) {
                std::string chunk = base64Result.substr(i, MAX_CHUNK_SIZE);

                rapidjson::StringBuffer chunkBuffer;
                rapidjson::Writer<rapidjson::StringBuffer> chunkWriter(chunkBuffer);
                chunkWriter.StartObject();
                chunkWriter.Key("type");
                chunkWriter.String("chunk_data");
                chunkWriter.Key("data");
                chunkWriter.String(chunk.c_str());
                chunkWriter.Key("offset");
                chunkWriter.Uint64(i);
                chunkWriter.EndObject();

                SendTo(hdl, chunkBuffer.GetString());
            }

            // 发送结束标记
            rapidjson::StringBuffer endBuffer;
            rapidjson::Writer<rapidjson::StringBuffer> endWriter(endBuffer);
            endWriter.StartObject();
            endWriter.Key("type");
            endWriter.String("chunk_end");
            endWriter.EndObject();
            SendTo(hdl, endBuffer.GetString());
        } else {
            // 小数据直接发送
            rapidjson::StringBuffer buffer;
            rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
            writer.StartObject();
            writer.Key("result");
            writer.String(result.c_str());
            writer.EndObject();
            SendTo(hdl, buffer.GetString());
        }
    }
    catch (const std::exception& e) {
        SendTo(hdl, "{\"error\":\"" + std::string(e.what()) + "\"}");
    }
}