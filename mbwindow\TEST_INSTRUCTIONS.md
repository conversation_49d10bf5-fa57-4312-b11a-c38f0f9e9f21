# Miniblink窗口工作区最大化测试说明

## 测试目标

验证miniblink窗口的最大化行为已从完全覆盖屏幕改为工作区最大化模式（不覆盖任务栏）。

## 编译和运行

1. **编译项目**
   ```bash
   # 使用Visual Studio命令提示符或配置好的编译环境
   cl /I. mb_window.cpp /link user32.lib gdi32.lib shlwapi.lib
   ```

2. **运行程序**
   - 确保`index.html`文件在可执行文件同目录下
   - 双击运行生成的可执行文件

## 测试步骤

### 1. 启动时最大化测试

**预期行为**:
- 窗口启动后自动最大化到工作区域
- 窗口顶部、左侧、右侧贴合屏幕边缘
- 窗口底部位于任务栏上方，任务栏保持可见
- 窗口没有圆角（最大化状态下圆角被移除）

**测试方法**:
1. 启动程序
2. 观察窗口是否正确占满工作区域
3. 检查任务栏是否仍然可见和可点击
4. 验证窗口是否没有圆角

### 2. 最大化/还原切换测试

**预期行为**:
- 点击最大化按钮时：窗口工作区最大化，任务栏可见
- 点击还原按钮时：窗口还原到78%屏幕大小并居中
- 还原后窗口应该有圆角效果

**测试方法**:
1. 在最大化状态下，通过JavaScript调用或UI按钮触发还原
2. 观察窗口是否还原到78%大小并居中
3. 检查还原后是否有圆角效果
4. 再次触发最大化，验证是否正确工作区最大化

### 3. 最小化恢复测试

**预期行为**:
- 从最小化状态恢复时，窗口应该工作区最大化
- 任务栏保持可见

**测试方法**:
1. 将窗口最小化到任务栏
2. 点击任务栏图标恢复窗口
3. 验证窗口是否正确工作区最大化

### 4. 多显示器测试（如果有多显示器）

**预期行为**:
- 在不同显示器上最大化时，应该适应该显示器的工作区域
- 每个显示器的任务栏都应该保持可见

**测试方法**:
1. 将窗口拖动到副显示器
2. 触发最大化操作
3. 验证窗口是否在正确的显示器上工作区最大化

### 5. 任务栏位置测试

**预期行为**:
- 无论任务栏在屏幕的哪个位置（底部、顶部、左侧、右侧），窗口都不应该覆盖任务栏

**测试方法**:
1. 将Windows任务栏移动到屏幕顶部
   - 右键任务栏 → 任务栏设置 → 任务栏在屏幕上的位置
2. 测试窗口最大化是否正确避开顶部任务栏
3. 重复测试左侧和右侧任务栏位置

## 验证要点

### ✅ 正确行为
- [ ] 窗口启动时自动工作区最大化
- [ ] 最大化时任务栏始终可见和可访问
- [ ] 窗口边界正确贴合工作区域边缘
- [ ] 最大化状态下窗口无圆角
- [ ] 还原到78%大小时窗口居中显示
- [ ] 还原状态下窗口有圆角效果
- [ ] 最大化/还原切换功能正常
- [ ] 从最小化恢复时正确工作区最大化
- [ ] 多显示器环境下行为正确
- [ ] 不同任务栏位置下都能正确工作

### ❌ 错误行为（需要修复）
- [ ] 窗口覆盖任务栏
- [ ] 最大化时窗口超出工作区域
- [ ] 任务栏被遮挡无法访问
- [ ] 最大化/还原功能失效
- [ ] 圆角效果异常
- [ ] 多显示器下行为异常

## 调试信息

程序运行时会输出调试信息到Visual Studio输出窗口或调试器：

- `"Window maximized to work area"` - 工作区最大化成功
- `"Window restored to 78% screen size"` - 还原到78%大小成功
- `"WM_SIZE: Window maximized, corners removed"` - 最大化时移除圆角
- `"WM_WINDOWPOSCHANGED: Maximized"` - 窗口状态变为最大化

## 常见问题

### Q: 窗口仍然覆盖任务栏
**A**: 检查是否所有`SW_MAXIMIZE`调用都已被替换为`MaximizeToWorkArea()`调用

### Q: 最大化/还原按钮不工作
**A**: 检查JavaScript与C++的通信是否正常，确认`onJsQuery`函数中的0x0002消息处理逻辑

### Q: 圆角效果异常
**A**: 检查`UpdateWindowRoundCorner`函数中的最大化状态判断是否包含`IsWorkAreaMaximized(hWnd)`

### Q: 多显示器下行为异常
**A**: 确认`MonitorFromWindow`和`GetMonitorInfo`API调用是否正确

## 性能注意事项

- 工作区最大化比系统最大化稍慢，因为需要计算工作区域
- 多显示器环境下需要额外的显示器信息查询
- 窗口属性的设置和清理会有轻微的内存开销

## 兼容性确认

- Windows XP及以上版本
- 支持高DPI显示器
- 兼容多显示器配置
- 支持不同的任务栏配置
