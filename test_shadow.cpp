// 简单的阴影测试程序
// 编译命令: cl test_shadow.cpp user32.lib gdi32.lib dwmapi.lib

#include <windows.h>
#include <dwmapi.h>
#include <iostream>

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "dwmapi.lib")

const wchar_t* CLASS_NAME = L"ShadowTestWindow";

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    case WM_PAINT: {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);
        
        // 绘制一个简单的背景
        RECT rect;
        GetClientRect(hwnd, &rect);
        HBRUSH brush = CreateSolidBrush(RGB(240, 240, 240));
        FillRect(hdc, &rect, brush);
        DeleteObject(brush);
        
        // 绘制文本
        SetBkMode(hdc, TRANSPARENT);
        SetTextColor(hdc, RGB(0, 0, 0));
        const wchar_t* text = L"阴影测试窗口\n\n如果您能看到窗口周围的阴影，\n说明阴影功能正常工作！\n\n请将窗口拖到不同颜色的背景上测试。";
        DrawTextW(hdc, text, -1, &rect, DT_CENTER | DT_VCENTER | DT_WORDBREAK);
        
        EndPaint(hwnd, &ps);
        return 0;
    }
    case WM_NCCALCSIZE:
        if (wParam) return 0; // 移除默认边框
        break;
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // 注册窗口类，包含阴影样式
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hbrBackground = NULL;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    // 关键：添加 CS_DROPSHADOW 样式
    wc.style = CS_HREDRAW | CS_VREDRAW | CS_DROPSHADOW;

    if (!RegisterClass(&wc)) {
        MessageBoxW(NULL, L"窗口类注册失败！", L"错误", MB_OK | MB_ICONERROR);
        return 1;
    }

    // 创建窗口
    HWND hwnd = CreateWindowExW(
        WS_EX_APPWINDOW,                    // 扩展样式
        CLASS_NAME,                         // 窗口类名
        L"阴影测试窗口",                    // 窗口标题
        WS_POPUP | WS_VISIBLE,              // 无边框窗口样式
        300, 200,                           // 位置
        400, 300,                           // 大小
        NULL, NULL, hInstance, NULL
    );

    if (!hwnd) {
        MessageBoxW(NULL, L"窗口创建失败！", L"错误", MB_OK | MB_ICONERROR);
        return 1;
    }

    // 尝试使用DWM增强阴影效果
    BOOL dwmEnabled = FALSE;
    if (SUCCEEDED(DwmIsCompositionEnabled(&dwmEnabled)) && dwmEnabled) {
        MARGINS margins = { 1, 1, 1, 1 };
        if (SUCCEEDED(DwmExtendFrameIntoClientArea(hwnd, &margins))) {
            std::wcout << L"DWM阴影增强已启用" << std::endl;
        }
    }

    // 创建圆角（可选）
    RECT rect;
    GetWindowRect(hwnd, &rect);
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;
    HRGN hRgn = CreateRoundRectRgn(0, 0, width, height, 15, 15);
    SetWindowRgn(hwnd, hRgn, TRUE);

    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);

    std::wcout << L"阴影测试窗口已创建" << std::endl;
    std::wcout << L"请观察窗口周围是否有阴影效果" << std::endl;
    std::wcout << L"按任意键关闭窗口..." << std::endl;

    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return 0;
}
