#pragma once
#include <string>
#include <memory>
#include "../WebSocket/WebSocketServer.h"

// WebSocket服务器实例
extern std::unique_ptr<WebSocketServerImpl> g_wsServer;

// 路径安全验证函数
bool IsPathSafe(const std::string& path);
std::string NormalizePath(const std::string& path);
std::string GetSafePath(const std::string& inputPath, const std::string& defaultSubdir = "");

// API函数声明
std::string Echo(const std::string& params);
std::string GetSystemTime(const std::string& params);

// 任务相关API
std::string SubmitTask(const std::string& params);
std::string GetTaskStatus(const std::string& params);
std::string GetTaskResult(const std::string& params);
std::string CancelTask(const std::string& params);

// 内存数据相关API
std::string GetMemoryData(const std::string& params);
std::string GetMemoryStats(const std::string& params);

// 注意: 所有DLL管理API已移除
// 现在使用自动DLL发现与加载机制


// 任务状态更新通知函数
void NotifyTaskStatusUpdate(const std::string& taskId, int status, const std::string& result = "", int progress = 0);

// WebSocket服务器线程函数
void WebSocketThread();

// API初始化和清理
bool InitializeAPI(int port = 9002);
void CleanupAPI();