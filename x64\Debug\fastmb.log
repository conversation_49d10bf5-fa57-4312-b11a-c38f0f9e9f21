﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\x64\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  Installing vcpkg dependencies to E:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\
  "E:\vcpkg-2025.01.13\vcpkg-2025.01.13\vcpkg.exe" install  --x-wait-for-lock --triplet "x64-windows" --vcpkg-root "E:\vcpkg-2025.01.13\vcpkg-2025.01.13\\" "--x-manifest-root=C:\Users\<USER>\Desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\\" "--x-install-root=E:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\\" 
  正在检测三元组 x64-windows 的编译器哈希...
  -- 正在使用环境变量中的 %HTTP(S)_PROXY%。
  已找到编译器: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/Hostx64/x64/cl.exe
  将移除以下包:
      asio:x86-windows
      boost-algorithm:x86-windows
      boost-align:x86-windows
      boost-array:x86-windows
      boost-asio:x86-windows
      boost-assert:x86-windows
      boost-bind:x86-windows
      boost-cmake:x86-windows
      boost-concept-check:x86-windows
      boost-config:x86-windows
      boost-container:x86-windows
      boost-container-hash:x86-windows
      boost-context:x86-windows
      boost-conversion:x86-windows
      boost-core:x86-windows
      boost-coroutine:x86-windows
      boost-date-time:x86-windows
      boost-describe:x86-windows
      boost-detail:x86-windows
      boost-exception:x86-windows
      boost-function:x86-windows
      boost-function-types:x86-windows
      boost-functional:x86-windows
      boost-fusion:x86-windows
      boost-headers:x86-windows
      boost-integer:x86-windows
      boost-intrusive:x86-windows
      boost-io:x86-windows
      boost-iterator:x86-windows
      boost-lexical-cast:x86-windows
      boost-move:x86-windows
      boost-mp11:x86-windows
      boost-mpl:x86-windows
      boost-numeric-conversion:x86-windows
      boost-optional:x86-windows
      boost-pool:x86-windows
      boost-predef:x86-windows
      boost-preprocessor:x86-windows
      boost-range:x86-windows
      boost-regex:x86-windows
      boost-smart-ptr:x86-windows
      boost-static-assert:x86-windows
      boost-system:x86-windows
      boost-throw-exception:x86-windows
      boost-tokenizer:x86-windows
      boost-tuple:x86-windows
      boost-type-traits:x86-windows
      boost-typeof:x86-windows
      boost-uninstall:x86-windows
      boost-unordered:x86-windows
      boost-utility:x86-windows
      boost-variant2:x86-windows
      boost-winapi:x86-windows
      nlohmann-json:x86-windows
      openssl:x86-windows
      rapidjson:x86-windows
      sqlite3:x86-windows
      websocketpp:x86-windows
      zlib:x86-windows
  将生成并安装以下包:
    * boost-algorithm:x64-windows@1.86.0
    * boost-align:x64-windows@1.86.0
    * boost-array:x64-windows@1.86.0
      boost-asio:x64-windows@1.86.0
    * boost-assert:x64-windows@1.86.0
    * boost-bind:x64-windows@1.86.0
    * boost-cmake:x64-windows@1.86.0
    * boost-concept-check:x64-windows@1.86.0
    * boost-config:x64-windows@1.86.0
    * boost-container:x64-windows@1.86.0
    * boost-container-hash:x64-windows@1.86.0
    * boost-context:x64-windows@1.86.0
    * boost-conversion:x64-windows@1.86.0
    * boost-core:x64-windows@1.86.0
    * boost-coroutine:x64-windows@1.86.0
      boost-date-time:x64-windows@1.86.0
    * boost-describe:x64-windows@1.86.0
    * boost-detail:x64-windows@1.86.0
    * boost-exception:x64-windows@1.86.0
    * boost-function:x64-windows@1.86.0
    * boost-function-types:x64-windows@1.86.0
    * boost-functional:x64-windows@1.86.0
    * boost-fusion:x64-windows@1.86.0
    * boost-headers:x64-windows@1.86.0
    * boost-integer:x64-windows@1.86.0
    * boost-intrusive:x64-windows@1.86.0
    * boost-io:x64-windows@1.86.0
    * boost-iterator:x64-windows@1.86.0
    * boost-lexical-cast:x64-windows@1.86.0
    * boost-move:x64-windows@1.86.0
    * boost-mp11:x64-windows@1.86.0
    * boost-mpl:x64-windows@1.86.0
    * boost-numeric-conversion:x64-windows@1.86.0
    * boost-optional:x64-windows@1.86.0
    * boost-pool:x64-windows@1.86.0
    * boost-predef:x64-windows@1.86.0
    * boost-preprocessor:x64-windows@1.86.0
    * boost-range:x64-windows@1.86.0
      boost-regex:x64-windows@1.86.0
    * boost-smart-ptr:x64-windows@1.86.0
    * boost-static-assert:x64-windows@1.86.0
      boost-system:x64-windows@1.86.0
    * boost-throw-exception:x64-windows@1.86.0
    * boost-tokenizer:x64-windows@1.86.0
    * boost-tuple:x64-windows@1.86.0
      boost-type-traits:x64-windows@1.86.0
    * boost-typeof:x64-windows@1.86.0
    * boost-unordered:x64-windows@1.86.0
    * boost-utility:x64-windows@1.86.0
    * boost-variant2:x64-windows@1.86.0
    * boost-winapi:x64-windows@1.86.0
      nlohmann-json:x64-windows@3.11.3#1
    * openssl:x64-windows@3.4.0#1
      rapidjson:x64-windows@2023-07-17#1
      sqlite3[core,json1]:x64-windows@3.47.2
      websocketpp[core,recommended]:x64-windows@0.8.2#3
    * zlib:x64-windows@1.3.1
  将修改其他包(*)以完成此操作。
  3.65 ms 后从 C:\Users\<USER>\AppData\Local\vcpkg\archives 还原了 0 个包。使用 --debug 了解更多详细信息。
  正在删除 1/116 个 sqlite3:x86-windows
  处理 sqlite3:x86-windows 所用时间: 10.1 ms
  正在删除 2/116 个 nlohmann-json:x86-windows
  处理 nlohmann-json:x86-windows 所用时间: 10.8 ms
  正在删除 3/116 个 websocketpp:x86-windows
  处理 websocketpp:x86-windows 所用时间: 18.6 ms
  正在删除 4/116 个 openssl:x86-windows
  处理 openssl:x86-windows 所用时间: 26.2 ms
  正在删除 5/116 个 zlib:x86-windows
  处理 zlib:x86-windows 所用时间: 4.87 ms
  正在删除 6/116 个 rapidjson:x86-windows
  处理 rapidjson:x86-windows 所用时间: 10 ms
  正在删除 7/116 个 boost-asio:x86-windows
  处理 boost-asio:x86-windows 所用时间: 117 ms
  正在删除 8/116 个 boost-align:x86-windows
  处理 boost-align:x86-windows 所用时间: 10.3 ms
  正在删除 9/116 个 boost-coroutine:x86-windows
  处理 boost-coroutine:x86-windows 所用时间: 9.84 ms
  正在删除 10/116 个 boost-context:x86-windows
  处理 boost-context:x86-windows 所用时间: 10.1 ms
  正在删除 11/116 个 boost-pool:x86-windows
  处理 boost-pool:x86-windows 所用时间: 6.34 ms
  正在删除 12/116 个 boost-date-time:x86-windows
  处理 boost-date-time:x86-windows 所用时间: 24 ms
  正在删除 13/116 个 boost-algorithm:x86-windows
  处理 boost-algorithm:x86-windows 所用时间: 18.2 ms
  正在删除 14/116 个 boost-exception:x86-windows
  处理 boost-exception:x86-windows 所用时间: 8.72 ms
  正在删除 15/116 个 boost-unordered:x86-windows
  处理 boost-unordered:x86-windows 所用时间: 10.1 ms
  正在删除 16/116 个 boost-lexical-cast:x86-windows
  处理 boost-lexical-cast:x86-windows 所用时间: 7.87 ms
  正在删除 17/116 个 boost-container:x86-windows
  处理 boost-container:x86-windows 所用时间: 16.5 ms
  正在删除 18/116 个 boost-intrusive:x86-windows
  处理 boost-intrusive:x86-windows 所用时间: 17.6 ms
  正在删除 19/116 个 boost-numeric-conversion:x86-windows
  处理 boost-numeric-conversion:x86-windows 所用时间: 7.86 ms
  正在删除 20/116 个 boost-range:x86-windows
  处理 boost-range:x86-windows 所用时间: 28.6 ms
  正在删除 21/116 个 boost-array:x86-windows
  处理 boost-array:x86-windows 所用时间: 4.92 ms
  正在删除 22/116 个 boost-conversion:x86-windows
  处理 boost-conversion:x86-windows 所用时间: 3.86 ms
  正在删除 23/116 个 boost-regex:x86-windows
  处理 boost-regex:x86-windows 所用时间: 18.1 ms
  正在删除 24/116 个 boost-integer:x86-windows
  处理 boost-integer:x86-windows 所用时间: 5.18 ms
  正在删除 25/116 个 boost-tokenizer:x86-windows
  处理 boost-tokenizer:x86-windows 所用时间: 3.81 ms
  正在删除 26/116 个 boost-iterator:x86-windows
  处理 boost-iterator:x86-windows 所用时间: 7.75 ms
  正在删除 27/116 个 boost-concept-check:x86-windows
  处理 boost-concept-check:x86-windows 所用时间: 5.28 ms
  正在删除 28/116 个 boost-fusion:x86-windows
  处理 boost-fusion:x86-windows 所用时间: 169 ms
  正在删除 29/116 个 boost-container-hash:x86-windows
  处理 boost-container-hash:x86-windows 所用时间: 8.3 ms
  正在删除 30/116 个 boost-describe:x86-windows
  处理 boost-describe:x86-windows 所用时间: 7.04 ms
  正在删除 31/116 个 boost-functional:x86-windows
  处理 boost-functional:x86-windows 所用时间: 4.92 ms
  正在删除 32/116 个 boost-function:x86-windows
  处理 boost-function:x86-windows 所用时间: 6.01 ms
  正在删除 33/116 个 boost-bind:x86-windows
  处理 boost-bind:x86-windows 所用时间: 6.03 ms
  正在删除 34/116 个 boost-function-types:x86-windows
  处理 boost-function-types:x86-windows 所用时间: 16.3 ms
  正在删除 35/116 个 boost-tuple:x86-windows
  处理 boost-tuple:x86-windows 所用时间: 3.5 ms
  正在删除 36/116 个 boost-typeof:x86-windows
  处理 boost-typeof:x86-windows 所用时间: 6.57 ms
  正在删除 37/116 个 boost-mpl:x86-windows
  处理 boost-mpl:x86-windows 所用时间: 155 ms
  正在删除 38/116 个 boost-optional:x86-windows
  处理 boost-optional:x86-windows 所用时间: 6.2 ms
  正在删除 39/116 个 boost-detail:x86-windows
  处理 boost-detail:x86-windows 所用时间: 6.83 ms
  正在删除 40/116 个 boost-smart-ptr:x86-windows
  处理 boost-smart-ptr:x86-windows 所用时间: 18.9 ms
  正在删除 41/116 个 boost-move:x86-windows
  处理 boost-move:x86-windows 所用时间: 11 ms
  正在删除 42/116 个 boost-utility:x86-windows
  处理 boost-utility:x86-windows 所用时间: 7.42 ms
  正在删除 43/116 个 boost-core:x86-windows
  处理 boost-core:x86-windows 所用时间: 14 ms
  正在删除 44/116 个 boost-io:x86-windows
  处理 boost-io:x86-windows 所用时间: 4.75 ms
  正在删除 45/116 个 boost-preprocessor:x86-windows
  处理 boost-preprocessor:x86-windows 所用时间: 69.8 ms
  正在删除 46/116 个 boost-type-traits:x86-windows
  处理 boost-type-traits:x86-windows 所用时间: 34.3 ms
  正在删除 47/116 个 boost-static-assert:x86-windows
  处理 boost-static-assert:x86-windows 所用时间: 3.15 ms
  正在删除 48/116 个 boost-system:x86-windows
  处理 boost-system:x86-windows 所用时间: 10.6 ms
  正在删除 49/116 个 boost-variant2:x86-windows
  处理 boost-variant2:x86-windows 所用时间: 5.34 ms
  正在删除 50/116 个 boost-mp11:x86-windows
  处理 boost-mp11:x86-windows 所用时间: 8.7 ms
  正在删除 51/116 个 boost-winapi:x86-windows
  处理 boost-winapi:x86-windows 所用时间: 22.8 ms
  正在删除 52/116 个 boost-predef:x86-windows
  处理 boost-predef:x86-windows 所用时间: 23.7 ms
  正在删除 53/116 个 boost-throw-exception:x86-windows
  处理 boost-throw-exception:x86-windows 所用时间: 2.97 ms
  正在删除 54/116 个 boost-assert:x86-windows
  处理 boost-assert:x86-windows 所用时间: 5.32 ms
  正在删除 55/116 个 boost-config:x86-windows
  处理 boost-config:x86-windows 所用时间: 39.2 ms
  正在删除 56/116 个 boost-headers:x86-windows
  处理 boost-headers:x86-windows 所用时间: 4.58 ms
  正在删除 57/116 个 boost-cmake:x86-windows
  处理 boost-cmake:x86-windows 所用时间: 3.46 ms
  正在删除 58/116 个 boost-uninstall:x86-windows
  处理 boost-uninstall:x86-windows 所用时间: 2.4 ms
  正在删除 59/116 个 asio:x86-windows
  处理 asio:x86-windows 所用时间: 88.5 ms
  正在安装 60/116 个 boost-cmake:x64-windows@1.86.0...
  正在生成 boost-cmake:x64-windows@1.86.0...
  -- Using cached boostorg-boost-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-boost-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-cmake/src/ost-1.86.0-da01bebc44.clean
  Downloading boostorg-cmake-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-cmake-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-cmake-boost-1.86.0.tar.gz
  -- Applying patch vcpkg-build.diff
  -- Applying patch fix-mpi.diff
  -- Applying patch no-prefix.diff
  -- Applying patch no-config-suffix.diff
  -- Applying patch no-honor-static.diff
  -- Applying patch add-optional-deps.diff
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-cmake/src/ost-1.86.0-196b676d9e.clean
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost-cmake/usage
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Up-to-date: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostFetch.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostInstall.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostMessage.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostRoot.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostTest.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost/cmake-build/BoostTestJamfile.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost-cmake/vcpkg-port-config.cmake
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-cmake_x64-windows/share/boost-cmake/copyright
  -- 正在执行生成后验证
  330 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-cmake:x64-windows 所用时间: 3.7 s
  boost-cmake:x64-windows 包 ABI: 2851381a9ec0c06bc4f5d41cfe1b915a8d808cabe3ce79d00bbf00aef5409da4
  正在安装 61/116 个 boost-headers:x64-windows@1.86.0...
  正在生成 boost-headers:x64-windows@1.86.0...
  Downloading boostorg-headers-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-headers-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-headers-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-headers/src/ost-1.86.0-7dc5ae651a.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-headers_x64-windows/share/boost-headers/copyright
  -- 正在执行生成后验证
  31.9 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-headers:x64-windows 所用时间: 3.3 s
  boost-headers:x64-windows 包 ABI: a7090606954f5e31871cf9629a33df4cfc4099577c524262bcb4e84d99346e0b
  正在安装 62/116 个 boost-config:x64-windows@1.86.0...
  正在生成 boost-config:x64-windows@1.86.0...
  Downloading boostorg-config-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-config-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-config-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-config/src/ost-1.86.0-8ca7c2ca3e.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-config_x64-windows/share/boost-config/copyright
  -- 正在执行生成后验证
  57.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-config:x64-windows 所用时间: 3.6 s
  boost-config:x64-windows 包 ABI: 26fd0381bd9c8e5addf6407c77a2074a6f9fefb2370fe0135fb8b024715cbf43
  正在安装 63/116 个 boost-assert:x64-windows@1.86.0...
  正在生成 boost-assert:x64-windows@1.86.0...
  Downloading boostorg-assert-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-assert-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-assert-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-assert/src/ost-1.86.0-cff5da31ed.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-assert_x64-windows/share/boost-assert/copyright
  -- 正在执行生成后验证
  28.5 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-assert:x64-windows 所用时间: 3.1 s
  boost-assert:x64-windows 包 ABI: 5432ced78b8b18fdbb7eb980241c83c5a90230affb474e923bb3bf32d446f895
  正在安装 64/116 个 boost-throw-exception:x64-windows@1.86.0...
  正在生成 boost-throw-exception:x64-windows@1.86.0...
  Downloading boostorg-throw_exception-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-throw_exception-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-throw_exception-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-throw-exception/src/ost-1.86.0-03f9559308.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-throw-exception_x64-windows/share/boost-throw-exception/copyright
  -- 正在执行生成后验证
  30.2 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-throw-exception:x64-windows 所用时间: 3 s
  boost-throw-exception:x64-windows 包 ABI: e2399be13d6b83e56728aa23634d38d4b52a3d921aa2c31b0050857d7d69d831
  正在安装 65/116 个 boost-predef:x64-windows@1.86.0...
  正在生成 boost-predef:x64-windows@1.86.0...
  Downloading boostorg-predef-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-predef-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-predef-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-predef/src/ost-1.86.0-5ef4c73c71.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-predef_x64-windows/share/boost-predef/copyright
  -- 正在执行生成后验证
  40.2 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-predef:x64-windows 所用时间: 3.2 s
  boost-predef:x64-windows 包 ABI: 34bd7a89e1e6c48e2cb4f7601a474b1985480aa3c87169fd2acc3db767a3341d
  正在安装 66/116 个 boost-winapi:x64-windows@1.86.0...
  正在生成 boost-winapi:x64-windows@1.86.0...
  Downloading boostorg-winapi-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-winapi-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-winapi-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-winapi/src/ost-1.86.0-afe0d2847f.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-winapi_x64-windows/share/boost-winapi/copyright
  -- 正在执行生成后验证
  53.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-winapi:x64-windows 所用时间: 3.2 s
  boost-winapi:x64-windows 包 ABI: 313f5d9b6026dd7c502522086eb0810f7e151d1967356dba513351666e6bfd49
  正在安装 67/116 个 boost-mp11:x64-windows@1.86.0...
  正在生成 boost-mp11:x64-windows@1.86.0...
  Downloading boostorg-mp11-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-mp11-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-mp11-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-mp11/src/ost-1.86.0-93cf4ea149.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-mp11_x64-windows/share/boost-mp11/copyright
  -- 正在执行生成后验证
  36.4 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-mp11:x64-windows 所用时间: 3.2 s
  boost-mp11:x64-windows 包 ABI: cb5e0b2c07e524ed6880bc8059ec696712b6bb00124febbb60080a2f4a6292ef
  正在安装 68/116 个 boost-variant2:x64-windows@1.86.0...
  正在生成 boost-variant2:x64-windows@1.86.0...
  Downloading boostorg-variant2-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-variant2-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-variant2-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-variant2/src/ost-1.86.0-ab70efdf36.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-variant2_x64-windows/share/boost-variant2/copyright
  -- 正在执行生成后验证
  34.4 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-variant2:x64-windows 所用时间: 3 s
  boost-variant2:x64-windows 包 ABI: 6c117c2188e78b680ec070a9234fc0cac4a0cb4cab33a6c30a7e87a946a69d7e
  正在安装 69/116 个 boost-system:x64-windows@1.86.0...
  正在生成 boost-system:x64-windows@1.86.0...
  CMake Warning at scripts/cmake/vcpkg_buildpath_length_warning.cmake:4 (message):
    boost-system's buildsystem uses very long paths and may fail on your
    system.
  
    We recommend moving vcpkg to a short path such as 'C:\src\vcpkg' or using
    the subst command.
  Call Stack (most recent call first):
    ports/boost-system/portfile.cmake:3 (vcpkg_buildpath_length_warning)
    scripts/ports.cmake:196 (include)
  
  
  Downloading boostorg-system-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-system-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-system-boost-1.86.0.tar.gz
  -- Applying patch compat.diff
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-system/src/ost-1.86.0-c57474edd7.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-system_x64-windows/share/boost-system/copyright
  -- 正在执行生成后验证
  84.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-system:x64-windows 所用时间: 4.8 s
  boost-system:x64-windows 包 ABI: a056523cf12435e4e1f07793c2600f60db0aff31f750837ba1b0279cddf793fe
  正在安装 70/116 个 boost-static-assert:x64-windows@1.86.0...
  正在生成 boost-static-assert:x64-windows@1.86.0...
  Downloading boostorg-static_assert-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-static_assert-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-static_assert-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-static-assert/src/ost-1.86.0-e7e646c10a.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-static-assert_x64-windows/share/boost-static-assert/copyright
  -- 正在执行生成后验证
  26.9 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-static-assert:x64-windows 所用时间: 3 s
  boost-static-assert:x64-windows 包 ABI: 7b46b9e26cd6cad1655700395f1cb48b75b75d7b052205cde39110ed9ebdc0b7
  正在安装 71/116 个 boost-type-traits:x64-windows@1.86.0...
  正在生成 boost-type-traits:x64-windows@1.86.0...
  Downloading boostorg-type_traits-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-type_traits-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-type_traits-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-type-traits/src/ost-1.86.0-53c11c29f7.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-type-traits_x64-windows/share/boost-type-traits/copyright
  -- 正在执行生成后验证
  51.8 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-type-traits:x64-windows 所用时间: 3.4 s
  boost-type-traits:x64-windows 包 ABI: 1459ce6f9d9e32aba61254a0b8a97af620f37fa7520e6bfda231b88bd4f06f66
  正在安装 72/116 个 boost-preprocessor:x64-windows@1.86.0...
  正在生成 boost-preprocessor:x64-windows@1.86.0...
  Downloading boostorg-preprocessor-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-preprocessor-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-preprocessor-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-preprocessor/src/ost-1.86.0-84266ceb21.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-preprocessor_x64-windows/share/boost-preprocessor/copyright
  -- 正在执行生成后验证
  136 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-preprocessor:x64-windows 所用时间: 4 s
  boost-preprocessor:x64-windows 包 ABI: c9a3b35b977a43ece17311bf31a4c26db049be74167e9fa825746ffaca404b42
  正在安装 73/116 个 boost-io:x64-windows@1.86.0...
  正在生成 boost-io:x64-windows@1.86.0...
  Downloading boostorg-io-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-io-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-io-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-io/src/ost-1.86.0-8ea8d85cc0.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-io_x64-windows/share/boost-io/copyright
  -- 正在执行生成后验证
  32.3 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-io:x64-windows 所用时间: 3 s
  boost-io:x64-windows 包 ABI: f331c47e5a61e475d5103640171ffc0274684c0f2b60a640c1d68b30bde7ee9a
  正在安装 74/116 个 boost-core:x64-windows@1.86.0...
  正在生成 boost-core:x64-windows@1.86.0...
  Downloading boostorg-core-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-core-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-core-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-core/src/ost-1.86.0-8eea1d4843.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-core_x64-windows/share/boost-core/copyright
  -- 正在执行生成后验证
  39.2 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-core:x64-windows 所用时间: 3.2 s
  boost-core:x64-windows 包 ABI: 283bc29f8c4b12ef90c4bdf3e853ff81cfdb635c1fcd400e11a19a0c2230af55
  正在安装 75/116 个 boost-utility:x64-windows@1.86.0...
  正在生成 boost-utility:x64-windows@1.86.0...
  Downloading boostorg-utility-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-utility-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-utility-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-utility/src/ost-1.86.0-6d9df6b1ea.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-utility_x64-windows/share/boost-utility/copyright
  -- 正在执行生成后验证
  41.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-utility:x64-windows 所用时间: 3.2 s
  boost-utility:x64-windows 包 ABI: 62731e196498b6439f7901d95c7326e88f55d4d1914b793fc3eb67dd815807f1
  正在安装 76/116 个 boost-move:x64-windows@1.86.0...
  正在生成 boost-move:x64-windows@1.86.0...
  Downloading boostorg-move-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-move-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-move-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-move/src/ost-1.86.0-a83c27c31c.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-move_x64-windows/share/boost-move/copyright
  -- 正在执行生成后验证
  42.3 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-move:x64-windows 所用时间: 3 s
  boost-move:x64-windows 包 ABI: 84a0e56eeec9f8a63dc923cc7b8edb27fa8b57a73f01ddd53bbaf82a460eb51d
  正在安装 77/116 个 boost-smart-ptr:x64-windows@1.86.0...
  正在生成 boost-smart-ptr:x64-windows@1.86.0...
  Downloading boostorg-smart_ptr-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-smart_ptr-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-smart_ptr-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-smart-ptr/src/ost-1.86.0-966ec184b2.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-smart-ptr_x64-windows/share/boost-smart-ptr/copyright
  -- 正在执行生成后验证
  39.3 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-smart-ptr:x64-windows 所用时间: 3.4 s
  boost-smart-ptr:x64-windows 包 ABI: 4673a23995b0db39fbfcd7ceafe8bedfa6889b28f80758c6fb381443d97c75d5
  正在安装 78/116 个 boost-detail:x64-windows@1.86.0...
  正在生成 boost-detail:x64-windows@1.86.0...
  Downloading boostorg-detail-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-detail-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-detail-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-detail/src/ost-1.86.0-3e51be0057.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-detail_x64-windows/share/boost-detail/copyright
  -- 正在执行生成后验证
  38.3 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-detail:x64-windows 所用时间: 3 s
  boost-detail:x64-windows 包 ABI: 198f8a36a0aeaa4ec174bd07b2be7b55e3a6ff6e11aa3a6df86df396b1027dc2
  正在安装 79/116 个 boost-optional:x64-windows@1.86.0...
  正在生成 boost-optional:x64-windows@1.86.0...
  Downloading boostorg-optional-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-optional-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-optional-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-optional/src/ost-1.86.0-3b4831e768.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-optional_x64-windows/share/boost-optional/copyright
  -- 正在执行生成后验证
  36.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-optional:x64-windows 所用时间: 3.1 s
  boost-optional:x64-windows 包 ABI: 2ddd9b957b4f038877c66f4ceee39c23d4f0adc7af4d3a2a0f0e5d85334bcdc4
  正在安装 80/116 个 boost-mpl:x64-windows@1.86.0...
  正在生成 boost-mpl:x64-windows@1.86.0...
  Downloading boostorg-mpl-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-mpl-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-mpl-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-mpl/src/ost-1.86.0-92630279e5.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-mpl_x64-windows/share/boost-mpl/copyright
  -- 正在执行生成后验证
  98 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-mpl:x64-windows 所用时间: 4.6 s
  boost-mpl:x64-windows 包 ABI: e5a0e43e840c389e1d949ce37bedf22736536aacd7ce3f39e0edc3f2d231568e
  正在安装 81/116 个 boost-typeof:x64-windows@1.86.0...
  正在生成 boost-typeof:x64-windows@1.86.0...
  Downloading boostorg-typeof-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-typeof-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-typeof-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-typeof/src/ost-1.86.0-9057ec91b0.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-typeof_x64-windows/share/boost-typeof/copyright
  -- 正在执行生成后验证
  32.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-typeof:x64-windows 所用时间: 3.1 s
  boost-typeof:x64-windows 包 ABI: 23836c02d50483b5cee94070ccc9a3aab7969ea648b115f5d7750cc8c2c1f38a
  正在安装 82/116 个 boost-tuple:x64-windows@1.86.0...
  正在生成 boost-tuple:x64-windows@1.86.0...
  Downloading boostorg-tuple-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-tuple-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-tuple-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-tuple/src/ost-1.86.0-1a4b66a7ed.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-tuple_x64-windows/share/boost-tuple/copyright
  -- 正在执行生成后验证
  32 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-tuple:x64-windows 所用时间: 3.1 s
  boost-tuple:x64-windows 包 ABI: 257df3ba66c844a9f9d1bf2375e25fda7ab543f1e4d3b09b623dc9639a9713ec
  正在安装 83/116 个 boost-function-types:x64-windows@1.86.0...
  正在生成 boost-function-types:x64-windows@1.86.0...
  Downloading boostorg-function_types-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-function_types-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-function_types-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-function-types/src/ost-1.86.0-aa3ea9a2df.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-function-types_x64-windows/share/boost-function-types/copyright
  -- 正在执行生成后验证
  44.6 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-function-types:x64-windows 所用时间: 3.2 s
  boost-function-types:x64-windows 包 ABI: 6366b88773225f5da247191e41dfdda81572ffec40632bd421fea8501b6ebcc5
  正在安装 84/116 个 boost-bind:x64-windows@1.86.0...
  正在生成 boost-bind:x64-windows@1.86.0...
  Downloading boostorg-bind-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-bind-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-bind-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-bind/src/ost-1.86.0-ba785d1d9b.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-bind_x64-windows/share/boost-bind/copyright
  -- 正在执行生成后验证
  33.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-bind:x64-windows 所用时间: 3 s
  boost-bind:x64-windows 包 ABI: c491c70b7a5feb4fa01f22fe576226d15f4302c25d82e3b4b5c7901ff125c4e0
  正在安装 85/116 个 boost-function:x64-windows@1.86.0...
  正在生成 boost-function:x64-windows@1.86.0...
  Downloading boostorg-function-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-function-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-function-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-function/src/ost-1.86.0-ec6eb5e976.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-function_x64-windows/share/boost-function/copyright
  -- 正在执行生成后验证
  37.8 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-function:x64-windows 所用时间: 3 s
  boost-function:x64-windows 包 ABI: 350a85f01b19f632af2a31b85996f7d6ee89fa5406b7361e87fae10334e5e6c1
  正在安装 86/116 个 boost-functional:x64-windows@1.86.0...
  正在生成 boost-functional:x64-windows@1.86.0...
  Downloading boostorg-functional-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-functional-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-functional-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-functional/src/ost-1.86.0-1c8b845386.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-functional_x64-windows/share/boost-functional/copyright
  -- 正在执行生成后验证
  35 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-functional:x64-windows 所用时间: 3.1 s
  boost-functional:x64-windows 包 ABI: ce5322c36350e17a6ed294118f32772ddcabc3991619c3969073ec3b4e7f2d9f
  正在安装 87/116 个 boost-describe:x64-windows@1.86.0...
  正在生成 boost-describe:x64-windows@1.86.0...
  Downloading boostorg-describe-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-describe-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-describe-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-describe/src/ost-1.86.0-b453081212.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-describe_x64-windows/share/boost-describe/copyright
  -- 正在执行生成后验证
  34.3 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-describe:x64-windows 所用时间: 3.1 s
  boost-describe:x64-windows 包 ABI: 718c2f41013081d02bcc010d1c8ac888ad3669ac9f3e625cdbc32c88c66b9efd
  正在安装 88/116 个 boost-container-hash:x64-windows@1.86.0...
  正在生成 boost-container-hash:x64-windows@1.86.0...
  Downloading boostorg-container_hash-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-container_hash-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-container_hash-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-container-hash/src/ost-1.86.0-9d5d7be400.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-container-hash_x64-windows/share/boost-container-hash/copyright
  -- 正在执行生成后验证
  34.2 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-container-hash:x64-windows 所用时间: 3 s
  boost-container-hash:x64-windows 包 ABI: a353b09b94c7e1371f1c4051f29f79aeee65a1ee4b726811aaa368015c1214fe
  正在安装 89/116 个 boost-fusion:x64-windows@1.86.0...
  正在生成 boost-fusion:x64-windows@1.86.0...
  Downloading boostorg-fusion-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-fusion-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-fusion-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-fusion/src/ost-1.86.0-b137a2029d.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-fusion_x64-windows/share/boost-fusion/copyright
  -- 正在执行生成后验证
  167 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-fusion:x64-windows 所用时间: 4.7 s
  boost-fusion:x64-windows 包 ABI: cd7f8f5990a662fe7dda61057d42730a69df3544c74d9406866c1ba081fff892
  正在安装 90/116 个 boost-concept-check:x64-windows@1.86.0...
  正在生成 boost-concept-check:x64-windows@1.86.0...
  Downloading boostorg-concept_check-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-concept_check-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-concept_check-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-concept-check/src/ost-1.86.0-1cfb0781dd.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-concept-check_x64-windows/share/boost-concept-check/copyright
  -- 正在执行生成后验证
  39.8 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-concept-check:x64-windows 所用时间: 3 s
  boost-concept-check:x64-windows 包 ABI: d02603b7216c6f0be7fa69a5b641430d097427647212241525e5eb185f09e8b2
  正在安装 91/116 个 boost-iterator:x64-windows@1.86.0...
  正在生成 boost-iterator:x64-windows@1.86.0...
  Downloading boostorg-iterator-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-iterator-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-iterator-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-iterator/src/ost-1.86.0-76a9f818a2.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-iterator_x64-windows/share/boost-iterator/copyright
  -- 正在执行生成后验证
  41.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-iterator:x64-windows 所用时间: 3.4 s
  boost-iterator:x64-windows 包 ABI: faa231a37f2955a88197eae8365c00e5f55870067dcc4a727f5a1f10f0582b1c
  正在安装 92/116 个 boost-tokenizer:x64-windows@1.86.0...
  正在生成 boost-tokenizer:x64-windows@1.86.0...
  Downloading boostorg-tokenizer-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-tokenizer-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-tokenizer-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-tokenizer/src/ost-1.86.0-99362d80bb.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-tokenizer_x64-windows/share/boost-tokenizer/copyright
  -- 正在执行生成后验证
  28.8 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-tokenizer:x64-windows 所用时间: 3 s
  boost-tokenizer:x64-windows 包 ABI: c995e2bf6bb506b7e80958e8090fa554175ae81d2ed99d9e910a51fa5c2589e5
  正在安装 93/116 个 boost-integer:x64-windows@1.86.0...
  正在生成 boost-integer:x64-windows@1.86.0...
  Downloading boostorg-integer-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-integer-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-integer-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-integer/src/ost-1.86.0-8c48ec5bcf.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-integer_x64-windows/share/boost-integer/copyright
  -- 正在执行生成后验证
  34 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-integer:x64-windows 所用时间: 3 s
  boost-integer:x64-windows 包 ABI: 36a0c8a7eccd419d3c38db3a333b6405725b2c8f1d6e86d44e9d6048f65d52e1
  正在安装 94/116 个 boost-regex:x64-windows@1.86.0...
  正在生成 boost-regex:x64-windows@1.86.0...
  Downloading boostorg-regex-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-regex-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-regex-boost-1.86.0.tar.gz
  -- Applying patch compat.diff
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-regex/src/ost-1.86.0-b1f6e91a9a.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-regex_x64-windows/share/boost-regex/copyright
  -- 正在执行生成后验证
  468 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-regex:x64-windows 所用时间: 9.4 s
  boost-regex:x64-windows 包 ABI: e351bee213e440b584d86cd86216665de68507cd7f1ed3fc4700e0523855d433
  正在安装 95/116 个 boost-conversion:x64-windows@1.86.0...
  正在生成 boost-conversion:x64-windows@1.86.0...
  Downloading boostorg-conversion-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-conversion-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-conversion-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-conversion/src/ost-1.86.0-7b341a92c3.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-conversion_x64-windows/share/boost-conversion/copyright
  -- 正在执行生成后验证
  28.8 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-conversion:x64-windows 所用时间: 3 s
  boost-conversion:x64-windows 包 ABI: 56322d5b090378990ca3e674ee36557d38d04a7fc1691de5a08f38d6cc1e9758
  正在安装 96/116 个 boost-array:x64-windows@1.86.0...
  正在生成 boost-array:x64-windows@1.86.0...
  Downloading boostorg-array-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-array-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-array-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-array/src/ost-1.86.0-c0e10e14f5.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-array_x64-windows/share/boost-array/copyright
  -- 正在执行生成后验证
  29 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-array:x64-windows 所用时间: 3 s
  boost-array:x64-windows 包 ABI: 4f276f269f7980b8d958c222c803204b32dddbc280e8bbdd7479852a1930ce29
  正在安装 97/116 个 boost-range:x64-windows@1.86.0...
  正在生成 boost-range:x64-windows@1.86.0...
  Downloading boostorg-range-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-range-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-range-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-range/src/ost-1.86.0-6d9bb3208e.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-range_x64-windows/share/boost-range/copyright
  -- 正在执行生成后验证
  45.5 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-range:x64-windows 所用时间: 3.4 s
  boost-range:x64-windows 包 ABI: 4b318c46789fc5562699fe2e5688d5798a87270aca22aa7d3e7a0dc6a435c00c
  正在安装 98/116 个 boost-numeric-conversion:x64-windows@1.86.0...
  正在生成 boost-numeric-conversion:x64-windows@1.86.0...
  Downloading boostorg-numeric_conversion-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-numeric_conversion-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-numeric_conversion-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-numeric-conversion/src/ost-1.86.0-b772f974c1.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-numeric-conversion_x64-windows/share/boost-numeric-conversion/copyright
  -- 正在执行生成后验证
  39.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-numeric-conversion:x64-windows 所用时间: 3.1 s
  boost-numeric-conversion:x64-windows 包 ABI: ada4ec443399d3e9d63f76ad127d552e3cfd94751a0a52697cabf7d3e4632a5f
  正在安装 99/116 个 boost-intrusive:x64-windows@1.86.0...
  正在生成 boost-intrusive:x64-windows@1.86.0...
  Downloading boostorg-intrusive-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-intrusive-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-intrusive-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-intrusive/src/ost-1.86.0-337295fefb.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-intrusive_x64-windows/share/boost-intrusive/copyright
  -- 正在执行生成后验证
  65.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-intrusive:x64-windows 所用时间: 3.2 s
  boost-intrusive:x64-windows 包 ABI: 049abe28c6628f7a4f88c5560255c50eaabe8e239e02cf32c909d775ab65009f
  正在安装 100/116 个 boost-container:x64-windows@1.86.0...
  正在生成 boost-container:x64-windows@1.86.0...
  Downloading boostorg-container-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-container-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-container-boost-1.86.0.tar.gz
  -- Applying patch posix-threads.diff
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-container/src/ost-1.86.0-bbec49e1ca.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-container_x64-windows/share/boost-container/copyright
  -- 正在执行生成后验证
  140 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-container:x64-windows 所用时间: 5.7 s
  boost-container:x64-windows 包 ABI: 192183d2a8b0a788d5a9089762e514e7771464e19cf6460f33e8370839b6f89d
  正在安装 101/116 个 boost-lexical-cast:x64-windows@1.86.0...
  正在生成 boost-lexical-cast:x64-windows@1.86.0...
  Downloading boostorg-lexical_cast-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-lexical_cast-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-lexical_cast-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-lexical-cast/src/ost-1.86.0-bff5d8212e.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-lexical-cast_x64-windows/share/boost-lexical-cast/copyright
  -- 正在执行生成后验证
  36.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-lexical-cast:x64-windows 所用时间: 3.2 s
  boost-lexical-cast:x64-windows 包 ABI: 96483994224856afe9cc3a75f92b0dfb5654d4c3afbe1e31c41fb71b2786930d
  正在安装 102/116 个 boost-unordered:x64-windows@1.86.0...
  正在生成 boost-unordered:x64-windows@1.86.0...
  Downloading boostorg-unordered-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-unordered-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-unordered-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-unordered/src/ost-1.86.0-459d142153.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-unordered_x64-windows/share/boost-unordered/copyright
  -- 正在执行生成后验证
  46.4 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-unordered:x64-windows 所用时间: 5.2 s
  boost-unordered:x64-windows 包 ABI: 154b5cb3535256cf68beddd085c2e54364349a8381ece4e6fe8dbe62f11a482a
  正在安装 103/116 个 boost-exception:x64-windows@1.86.0...
  正在生成 boost-exception:x64-windows@1.86.0...
  Downloading boostorg-exception-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-exception-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-exception-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-exception/src/ost-1.86.0-6fac1ea9e9.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-exception_x64-windows/share/boost-exception/copyright
  -- 正在执行生成后验证
  35.5 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-exception:x64-windows 所用时间: 3.4 s
  boost-exception:x64-windows 包 ABI: 25eab475f03a091256032c2b07c55c81334d27048dfd6c66a7064c5193133370
  正在安装 104/116 个 boost-algorithm:x64-windows@1.86.0...
  正在生成 boost-algorithm:x64-windows@1.86.0...
  Downloading boostorg-algorithm-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-algorithm-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-algorithm-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-algorithm/src/ost-1.86.0-78901f118f.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-algorithm_x64-windows/share/boost-algorithm/copyright
  -- 正在执行生成后验证
  41.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-algorithm:x64-windows 所用时间: 3.5 s
  boost-algorithm:x64-windows 包 ABI: ff23059ef1c835332e475e83dd11452fdfa40bb3f54d2aa192c0afed46f1d7ec
  正在安装 105/116 个 boost-date-time:x64-windows@1.86.0...
  正在生成 boost-date-time:x64-windows@1.86.0...
  Downloading boostorg-date_time-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-date_time-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-date_time-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-date-time/src/ost-1.86.0-0ee3268484.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-date-time_x64-windows/share/boost-date-time/copyright
  -- 正在执行生成后验证
  95.1 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-date-time:x64-windows 所用时间: 4.5 s
  boost-date-time:x64-windows 包 ABI: 4e026d9b93cdc747fa8d88b9a695a72ee05da734f269fac2627c62b117b68738
  正在安装 106/116 个 boost-pool:x64-windows@1.86.0...
  正在生成 boost-pool:x64-windows@1.86.0...
  Downloading boostorg-pool-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-pool-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-pool-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-pool/src/ost-1.86.0-cd045b2676.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-pool_x64-windows/share/boost-pool/copyright
  -- 正在执行生成后验证
  41.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-pool:x64-windows 所用时间: 3.3 s
  boost-pool:x64-windows 包 ABI: a38436e321f8a232f2353418f73749260ff408e26f235d2ff800e32a61014f93
  正在安装 107/116 个 boost-context:x64-windows@1.86.0...
  正在生成 boost-context:x64-windows@1.86.0...
  Downloading boostorg-context-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-context-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-context-boost-1.86.0.tar.gz
  -- Applying patch marmasm.patch
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-context/src/ost-1.86.0-42b4fad533.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-context_x64-windows/share/boost-context/copyright
  -- 正在执行生成后验证
  109 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-context:x64-windows 所用时间: 5.3 s
  boost-context:x64-windows 包 ABI: 8706a3dc888fe954800fc55b7deab66f120b53e2a76cf54a0374630a36ddb949
  正在安装 108/116 个 boost-coroutine:x64-windows@1.86.0...
  正在生成 boost-coroutine:x64-windows@1.86.0...
  Downloading boostorg-coroutine-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-coroutine-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-coroutine-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-coroutine/src/ost-1.86.0-6eefa8f68f.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-coroutine_x64-windows/share/boost-coroutine/copyright
  -- 正在执行生成后验证
  172 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-coroutine:x64-windows 所用时间: 5 s
  boost-coroutine:x64-windows 包 ABI: dccbba9726b41105ce335e4969e0f553d5a322ba37e95ccf10cb4ff9109d8927
  正在安装 109/116 个 boost-align:x64-windows@1.86.0...
  正在生成 boost-align:x64-windows@1.86.0...
  Downloading boostorg-align-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-align-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-align-boost-1.86.0.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-align/src/ost-1.86.0-a246ed6c77.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-align_x64-windows/share/boost-align/copyright
  -- 正在执行生成后验证
  35.7 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-align:x64-windows 所用时间: 3.2 s
  boost-align:x64-windows 包 ABI: 643ae603b18fefa4dfd84f466e5f5760ab8df8d220bac4c968e6cf32bba3b61e
  正在安装 110/116 个 boost-asio:x64-windows@1.86.0...
  正在生成 boost-asio:x64-windows@1.86.0...
  Downloading boostorg-asio-boost-1.86.0.tar.gz
  Successfully downloaded boostorg-asio-boost-1.86.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/boostorg-asio-boost-1.86.0.tar.gz
  -- Applying patch opt-dep.diff
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/boost-asio/src/ost-1.86.0-cf1f3e6264.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/boost-asio_x64-windows/share/boost-asio/copyright
  -- 正在执行生成后验证
  107 ms 后在 1 个目标中存储了二进制文件。
  处理 boost-asio:x64-windows 所用时间: 4.5 s
  boost-asio:x64-windows 包 ABI: f1b8dedd5461be3c038a7ac6388cbc564d95bc4125257343f3f82917049ab306
  正在安装 111/116 个 nlohmann-json:x64-windows@3.11.3#1...
  正在生成 nlohmann-json:x64-windows@3.11.3#1...
  -- Using cached nlohmann-json-v3.11.3.tar.gz.
  -- Cleaning sources at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/nlohmann-json/src/v3.11.3-434e7fbc3e.clean. Use --editable to skip cleaning for the packages you specify.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/nlohmann-json-v3.11.3.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/nlohmann-json/src/v3.11.3-434e7fbc3e.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/nlohmann-json_x64-windows/share/pkgconfig/nlohmann_json.pc
  -- Using cached msys2-mingw-w64-x86_64-pkgconf-1~2.3.0-1-any.pkg.tar.zst.
  -- Using cached msys2-msys2-runtime-3.5.4-2-x86_64.pkg.tar.zst.
  -- Using msys root at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/tools/msys2/21caed2f81ec917b
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/nlohmann-json_x64-windows/debug/share/pkgconfig/nlohmann_json.pc
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/nlohmann-json_x64-windows/share/nlohmann-json/copyright
  -- 正在执行生成后验证
  64.3 ms 后在 1 个目标中存储了二进制文件。
  处理 nlohmann-json:x64-windows 所用时间: 2.6 s
  nlohmann-json:x64-windows 包 ABI: accfaa87199df556f96216c759bd2599043166702e281716ffd818b8ffd5676b
  正在安装 112/116 个 rapidjson:x64-windows@2023-07-17#1...
  正在生成 rapidjson:x64-windows@2023-07-17#1...
  Downloading Tencent-rapidjson-a95e013b97ca6523f32da23f5095fcc9dd6067e5-2.tar.gz
  Successfully downloaded Tencent-rapidjson-a95e013b97ca6523f32da23f5095fcc9dd6067e5-2.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/Tencent-rapidjson-a95e013b97ca6523f32da23f5095fcc9dd6067e5-2.tar.gz
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/rapidjson/src/c9dd6067e5-8a215fc499.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Using cached msys2-mingw-w64-x86_64-pkgconf-1~2.3.0-1-any.pkg.tar.zst.
  -- Using cached msys2-msys2-runtime-3.5.4-2-x86_64.pkg.tar.zst.
  -- Using msys root at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/tools/msys2/21caed2f81ec917b
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/rapidjson_x64-windows/share/rapidjson/copyright
  -- 正在执行生成后验证
  61.2 ms 后在 1 个目标中存储了二进制文件。
  处理 rapidjson:x64-windows 所用时间: 4.1 s
  rapidjson:x64-windows 包 ABI: b3f6c05ce0f09fbe9ada7452b369d94911f3fdc592ddb394e6a2f45985fe54c3
  正在安装 113/116 个 sqlite3[core,json1]:x64-windows@3.47.2...
  正在生成 sqlite3[core,json1]:x64-windows@3.47.2...
  Downloading sqlite-autoconf-3470200.zip
  Successfully downloaded sqlite-autoconf-3470200.zip.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/sqlite-autoconf-3470200.zip
  -- Applying patch fix-arm-uwp.patch
  -- Applying patch add-config-include.patch
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/sqlite3/src/nf-3470200-ddffcf69ff.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/sqlite3_x64-windows/lib/pkgconfig/sqlite3.pc
  -- Using cached msys2-mingw-w64-x86_64-pkgconf-1~2.3.0-1-any.pkg.tar.zst.
  -- Using cached msys2-msys2-runtime-3.5.4-2-x86_64.pkg.tar.zst.
  -- Using msys root at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/tools/msys2/21caed2f81ec917b
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/sqlite3_x64-windows/debug/lib/pkgconfig/sqlite3.pc
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/sqlite3_x64-windows/share/sqlite3/usage
  -- 正在执行生成后验证
  418 ms 后在 1 个目标中存储了二进制文件。
  处理 sqlite3:x64-windows 所用时间: 17 s
  sqlite3:x64-windows 包 ABI: c713beacbb3878c1b938c3c12ca5ad3dbd0a1539147794c14ae26096d8f336cd
  正在安装 114/116 个 zlib:x64-windows@1.3.1...
  正在生成 zlib:x64-windows@1.3.1...
  Downloading madler-zlib-v1.3.1.tar.gz
  Successfully downloaded madler-zlib-v1.3.1.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/madler-zlib-v1.3.1.tar.gz
  -- Applying patch 0001-Prevent-invalid-inclusions-when-HAVE_-is-set-to-0.patch
  -- Applying patch 0002-build-static-or-shared-not-both.patch
  -- Applying patch 0003-android-and-mingw-fixes.patch
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/zlib/src/v1.3.1-2e5db616bf.clean
  -- Found external ninja('1.12.1').
  -- Configuring x64-windows
  -- Building x64-windows-dbg
  -- Building x64-windows-rel
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/zlib_x64-windows/share/zlib/vcpkg-cmake-wrapper.cmake
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/zlib_x64-windows/lib/pkgconfig/zlib.pc
  -- Using cached msys2-mingw-w64-x86_64-pkgconf-1~2.3.0-1-any.pkg.tar.zst.
  -- Using cached msys2-msys2-runtime-3.5.4-2-x86_64.pkg.tar.zst.
  -- Using msys root at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/tools/msys2/21caed2f81ec917b
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/zlib_x64-windows/debug/lib/pkgconfig/zlib.pc
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/zlib_x64-windows/share/zlib/copyright
  -- 正在执行生成后验证
  129 ms 后在 1 个目标中存储了二进制文件。
  处理 zlib:x64-windows 所用时间: 7.7 s
  zlib:x64-windows 包 ABI: 6278e39ee03dde4cbabf63d4793f28dd1f8007fff5a0743827699759dad66c10
  正在安装 115/116 个 openssl:x64-windows@3.4.0#1...
  正在生成 openssl:x64-windows@3.4.0#1...
  Downloading openssl-openssl-openssl-3.4.0.tar.gz
  Successfully downloaded openssl-openssl-openssl-3.4.0.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/openssl-openssl-openssl-3.4.0.tar.gz
  -- Applying patch cmake-config.patch
  -- Applying patch command-line-length.patch
  -- Applying patch mkbuildinf.diff
  -- Applying patch script-prefix.patch
  -- Applying patch asm-armcap.patch
  -- Applying patch windows/install-layout.patch
  -- Applying patch windows/install-pdbs.patch
  -- Applying patch unix/android-cc.patch
  -- Applying patch unix/move-openssldir.patch
  -- Applying patch unix/no-empty-dirs.patch
  -- Applying patch unix/no-static-libs-for-shared.patch
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/openssl/src/nssl-3.4.0-26c53982ba.clean
  -- Found external ninja('1.12.1').
  -- Getting CMake variables for x64-windows
  Downloading nasm-2.16.03-win64.zip
  Successfully downloaded nasm-2.16.03-win64.zip.
  -- Getting CMake variables for x64-windows
  Downloading jom_1_1_4.zip
  Successfully downloaded jom_1_1_4.zip.
  -- Prerunning x64-windows-dbg
  -- Building x64-windows-dbg
  -- Prerunning x64-windows-rel
  -- Building x64-windows-rel
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/lib/pkgconfig/libcrypto.pc
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/lib/pkgconfig/libssl.pc
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/lib/pkgconfig/openssl.pc
  -- Using cached msys2-mingw-w64-x86_64-pkgconf-1~2.3.0-1-any.pkg.tar.zst.
  -- Using cached msys2-msys2-runtime-3.5.4-2-x86_64.pkg.tar.zst.
  -- Using msys root at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/tools/msys2/21caed2f81ec917b
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/debug/lib/pkgconfig/libcrypto.pc
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/debug/lib/pkgconfig/libssl.pc
  -- Fixing pkgconfig file: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/debug/lib/pkgconfig/openssl.pc
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/share/openssl/usage
  -- Installing: E:/vcpkg-2025.01.13/vcpkg-2025.01.13/packages/openssl_x64-windows/share/openssl/copyright
  -- 正在执行生成后验证
  1.9 s 后在 1 个目标中存储了二进制文件。
  处理 openssl:x64-windows 所用时间: 3.1 min
  openssl:x64-windows 包 ABI: b246d28b07dfd9f3069ed20603139ab18da0e9f66751ad144507417b0fe16021
  正在安装 116/116 个 websocketpp[core,recommended]:x64-windows@0.8.2#3...
  正在生成 websocketpp[core,recommended]:x64-windows@0.8.2#3...
  -- Using cached zaphoyd-websocketpp-56123c87598f8b1dd471be83ca841ceae07f95ba.tar.gz.
  -- Extracting source E:/vcpkg-2025.01.13/vcpkg-2025.01.13/downloads/zaphoyd-websocketpp-56123c87598f8b1dd471be83ca841ceae07f95ba.tar.gz
  -- Applying patch cxx20.patch
  -- Using source at E:/vcpkg-2025.01.13/vcpkg-2025.01.13/buildtrees/websocketpp/src/eae07f95ba-231f01af2c.clean
  -- 正在执行生成后验证
  48.2 ms 后在 1 个目标中存储了二进制文件。
  处理 websocketpp:x64-windows 所用时间: 571 ms
  websocketpp:x64-windows 包 ABI: 262a958d7430c29b0bb2e6541755ca1feac5510a38aadf4ff29c0749df2daf01
  总安装时间: 6.7 min
  asio 提供 CMake 目标:
  
    # 这是启发式生成的，并且可能不正确
    find_package(asio CONFIG REQUIRED)
    target_link_libraries(main PRIVATE asio::asio)
  
  asio 提供 pkg-config 模块:
  
    # A cross-platform C++ library for network and low-level I/O programming that provides developers with a consistent asynchronous model using a modern C++ approach.
    asio
  
  The package boost-system is compatible with built-in CMake targets of FindBoost.cmake:
  
      find_package(Boost REQUIRED COMPONENTS system)
      target_link_libraries(main PRIVATE Boost::system)
  
  or the generated cmake configs via:
  
      find_package(boost_system REQUIRED CONFIG)
      target_link_libraries(main PRIVATE Boost::system)
  
  The package boost-type-traits is compatible with built-in CMake targets of FindBoost.cmake:
  
      find_package(Boost REQUIRED COMPONENTS type_traits)
      target_link_libraries(main PRIVATE Boost::type_traits)
  
  or the generated cmake configs via:
  
      find_package(boost_type_traits REQUIRED CONFIG)
      target_link_libraries(main PRIVATE Boost::type_traits)
  
  The package boost-regex is compatible with built-in CMake targets of FindBoost.cmake:
  
      find_package(Boost REQUIRED COMPONENTS regex)
      target_link_libraries(main PRIVATE Boost::regex)
  
  or the generated cmake configs via:
  
      find_package(boost_regex REQUIRED CONFIG)
      target_link_libraries(main PRIVATE Boost::regex)
  
  The package boost-date-time is compatible with built-in CMake targets of FindBoost.cmake:
  
      find_package(Boost REQUIRED COMPONENTS date_time)
      target_link_libraries(main PRIVATE Boost::date_time)
  
  or the generated cmake configs via:
  
      find_package(boost_date_time REQUIRED CONFIG)
      target_link_libraries(main PRIVATE Boost::date_time)
  
  The package boost-asio is compatible with built-in CMake targets of FindBoost.cmake:
  
      find_package(Boost REQUIRED COMPONENTS asio)
      target_link_libraries(main PRIVATE Boost::asio)
  
  or the generated cmake configs via:
  
      find_package(boost_asio REQUIRED CONFIG)
      target_link_libraries(main PRIVATE Boost::asio)
  
  The package nlohmann-json provides CMake targets:
  
      find_package(nlohmann_json CONFIG REQUIRED)
      target_link_libraries(main PRIVATE nlohmann_json::nlohmann_json)
  
  The package nlohmann-json can be configured to not provide implicit conversions via a custom triplet file:
  
      set(nlohmann-json_IMPLICIT_CONVERSIONS OFF)
  
  For more information, see the docs here:
      
      https://json.nlohmann.me/api/macros/json_use_implicit_conversions/
  
  rapidjson 提供 CMake 目标:
  
    # 这是启发式生成的，并且可能不正确
    find_package(RapidJSON CONFIG REQUIRED)
    target_link_libraries(main PRIVATE rapidjson)
  
  sqlite3 provides pkgconfig bindings.
  sqlite3 provides CMake targets:
  
      find_package(unofficial-sqlite3 CONFIG REQUIRED)
      target_link_libraries(main PRIVATE unofficial::sqlite3::sqlite3)
  
  websocketpp 提供 CMake 目标:
  
    # 这是启发式生成的，并且可能不正确
    find_package(websocketpp CONFIG REQUIRED)
    target_link_libraries(main PRIVATE websocketpp::websocketpp)
  
  main.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\main.cpp(59): fatal error C1010: 在查找预编译头时遇到意外的文件结尾。是否忘记了向源中添加“#include "stdafx.h"”?
