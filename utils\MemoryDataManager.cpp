﻿#include "MemoryDataManager.h"
#include "Logger.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

MemoryDataManager& MemoryDataManager::getInstance() {
    static MemoryDataManager instance;
    return instance;
}

bool MemoryDataManager::initialize() {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        // 清空现有数据
        taskDataList_.clear();
        jsonResultList_.clear();
        taskIdIndex_.clear();
        dllFuncIndex_.clear();
        
        LogInfo("MemoryDataManager initialized successfully");
        return true;
    } catch (const std::exception& e) {
        setError("Failed to initialize MemoryDataManager: " + std::string(e.what()));
        LogError(lastError_);
        return false;
    }
}

void MemoryDataManager::cleanup() {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        LogInfo("Cleaning up MemoryDataManager, task data count: " + std::to_string(taskDataList_.size()) + 
                ", JSON result count: " + std::to_string(jsonResultList_.size()));
        
        taskDataList_.clear();
        jsonResultList_.clear();
        taskIdIndex_.clear();
        dllFuncIndex_.clear();
        
        LogInfo("MemoryDataManager cleanup complete");
    } catch (const std::exception& e) {
        LogError("Exception during MemoryDataManager cleanup: " + std::string(e.what()));
    }
}

bool MemoryDataManager::insertTaskData(const TaskData& data) {
    return insertTaskData(MemoryTaskData::fromTaskData(data));
}

bool MemoryDataManager::insertTaskData(const MemoryTaskData& data) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        size_t index = taskDataList_.size();
        taskDataList_.push_back(data);
        
        // 更新索引
        updateTaskIdIndex(data.taskId, index);
        updateDllFuncIndex(data.dllName, data.funcName, index);
        
        LogDebug("Inserted task data to memory: " + data.taskId + " (" + data.dllName + "::" + data.funcName + ")");
        return true;
    } catch (const std::exception& e) {
        setError("Failed to insert task data: " + std::string(e.what()));
        LogError(lastError_);
        return false;
    }
}

std::vector<MemoryTaskData> MemoryDataManager::getLatestTaskData(int limit) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        std::vector<MemoryTaskData> result;
        int count = 0;
        
        // 从最新的开始返回
        for (auto it = taskDataList_.rbegin(); it != taskDataList_.rend() && count < limit; ++it, ++count) {
            result.push_back(*it);
        }
        
        return result;
    } catch (const std::exception& e) {
        setError("Failed to get latest task data: " + std::string(e.what()));
        LogError(lastError_);
        return {};
    }
}

std::vector<MemoryTaskData> MemoryDataManager::getTaskDataByTaskId(const std::string& taskId, const std::string& dataType) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        std::vector<MemoryTaskData> result;
        auto it = taskIdIndex_.find(taskId);
        if (it != taskIdIndex_.end()) {
            for (size_t index : it->second) {
                if (index < taskDataList_.size()) {
                    const auto& data = taskDataList_[index];
                    if (dataType.empty() || data.dataType == dataType) {
                        result.push_back(data);
                    }
                }
            }
        }
        
        return result;
    } catch (const std::exception& e) {
        setError("Failed to get task data by ID: " + std::string(e.what()));
        LogError(lastError_);
        return {};
    }
}

MemoryTaskData MemoryDataManager::getTaskResultByTaskId(const std::string& taskId) {
    auto results = getTaskDataByTaskId(taskId, "result");
    if (!results.empty()) {
        return results[0]; // 返回第一个结果
    }
    return MemoryTaskData{}; // 返回空数据
}

bool MemoryDataManager::insertJsonResult(const std::string& taskId, const std::string& dllName, 
                                        const std::string& funcName, const json& jsonData, 
                                        const std::string& params) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        MemoryJsonResult result;
        result.taskId = taskId;
        result.dllName = dllName;
        result.funcName = funcName;
        result.jsonData = jsonData;
        result.timestamp = getCurrentTimestamp();
        result.params = params;
        
        jsonResultList_.push_back(result);
        
        LogDebug("Inserted JSON result to memory: " + taskId + " (" + dllName + "::" + funcName + ")");
        return true;
    } catch (const std::exception& e) {
        setError("Failed to insert JSON result: " + std::string(e.what()));
        LogError(lastError_);
        return false;
    }
}

std::vector<MemoryJsonResult> MemoryDataManager::getJsonResults(const std::string& dllName, 
                                                              const std::string& funcName, 
                                                              int limit) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        std::vector<MemoryJsonResult> result;
        int count = 0;
        
        for (auto it = jsonResultList_.rbegin(); it != jsonResultList_.rend() && count < limit; ++it) {
            bool match = true;
            if (!dllName.empty() && it->dllName != dllName) match = false;
            if (!funcName.empty() && it->funcName != funcName) match = false;
            
            if (match) {
                result.push_back(*it);
                count++;
            }
        }
        
        return result;
    } catch (const std::exception& e) {
        setError("Failed to get JSON results: " + std::string(e.what()));
        LogError(lastError_);
        return {};
    }
}

MemoryJsonResult MemoryDataManager::getJsonResultByTaskId(const std::string& taskId) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        for (const auto& result : jsonResultList_) {
            if (result.taskId == taskId) {
                return result;
            }
        }
        
        return MemoryJsonResult{}; // 返回空结果
    } catch (const std::exception& e) {
        setError("Failed to get JSON result by task ID: " + std::string(e.what()));
        LogError(lastError_);
        return MemoryJsonResult{};
    }
}

bool MemoryDataManager::batchInsertTaskData(const std::vector<TaskData>& dataList) {
    std::vector<MemoryTaskData> memDataList;
    memDataList.reserve(dataList.size());
    
    for (const auto& data : dataList) {
        memDataList.push_back(MemoryTaskData::fromTaskData(data));
    }
    
    return batchInsertTaskData(memDataList);
}

bool MemoryDataManager::batchInsertTaskData(const std::vector<MemoryTaskData>& dataList) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        size_t startIndex = taskDataList_.size();
        taskDataList_.reserve(taskDataList_.size() + dataList.size());
        
        for (size_t i = 0; i < dataList.size(); ++i) {
            const auto& data = dataList[i];
            taskDataList_.push_back(data);
            
            // 更新索引
            size_t index = startIndex + i;
            updateTaskIdIndex(data.taskId, index);
            updateDllFuncIndex(data.dllName, data.funcName, index);
        }
        
        LogInfo("Batch inserted " + std::to_string(dataList.size()) + " task data records to memory");
        return true;
    } catch (const std::exception& e) {
        setError("Failed to batch insert task data: " + std::string(e.what()));
        LogError(lastError_);
        return false;
    }
}

std::vector<MemoryTaskData> MemoryDataManager::getResultsByDllAndFunc(const std::string& dllName, 
                                                                     const std::string& funcName, 
                                                                     int limit) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        std::vector<MemoryTaskData> result;
        std::string key = generateDllFuncKey(dllName, funcName);
        
        auto it = dllFuncIndex_.find(key);
        if (it != dllFuncIndex_.end()) {
            int count = 0;
            // 从最新的开始返回
            for (auto indexIt = it->second.rbegin(); indexIt != it->second.rend() && count < limit; ++indexIt, ++count) {
                if (*indexIt < taskDataList_.size()) {
                    const auto& data = taskDataList_[*indexIt];
                    if (data.dataType == "result") { // 只返回结果数据
                        result.push_back(data);
                    }
                }
            }
        }
        
        return result;
    } catch (const std::exception& e) {
        setError("Failed to get results by DLL and function: " + std::string(e.what()));
        LogError(lastError_);
        return {};
    }
}

bool MemoryDataManager::cleanupOldData(int minutesToKeep) {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        auto now = std::chrono::system_clock::now();
        auto cutoffTime = now - std::chrono::minutes(minutesToKeep);
        
        size_t originalTaskCount = taskDataList_.size();
        size_t originalJsonCount = jsonResultList_.size();
        
        // 清理任务数据
        taskDataList_.erase(
            std::remove_if(taskDataList_.begin(), taskDataList_.end(),
                [cutoffTime](const MemoryTaskData& data) {
                    // 简单的时间比较，实际应该解析timestamp
                    return false; // 暂时不删除，可以根据需要实现
                }),
            taskDataList_.end()
        );
        
        // 清理JSON结果
        jsonResultList_.erase(
            std::remove_if(jsonResultList_.begin(), jsonResultList_.end(),
                [cutoffTime](const MemoryJsonResult& result) {
                    return false; // 暂时不删除，可以根据需要实现
                }),
            jsonResultList_.end()
        );
        
        // 重建索引
        taskIdIndex_.clear();
        dllFuncIndex_.clear();
        for (size_t i = 0; i < taskDataList_.size(); ++i) {
            const auto& data = taskDataList_[i];
            updateTaskIdIndex(data.taskId, i);
            updateDllFuncIndex(data.dllName, data.funcName, i);
        }
        
        LogInfo("Cleanup completed. Task data: " + std::to_string(originalTaskCount) + " -> " + 
                std::to_string(taskDataList_.size()) + ", JSON results: " + 
                std::to_string(originalJsonCount) + " -> " + std::to_string(jsonResultList_.size()));
        
        return true;
    } catch (const std::exception& e) {
        setError("Failed to cleanup old data: " + std::string(e.what()));
        LogError(lastError_);
        return false;
    }
}

size_t MemoryDataManager::getTaskDataCount() const {
    std::lock_guard<std::mutex> lock(dataMutex_);
    return taskDataList_.size();
}

size_t MemoryDataManager::getJsonResultCount() const {
    std::lock_guard<std::mutex> lock(dataMutex_);
    return jsonResultList_.size();
}

json MemoryDataManager::exportAllData() const {
    json result;
    result["taskData"] = exportTaskData();
    result["jsonResults"] = exportJsonResults();
    result["timestamp"] = getCurrentTimestamp();
    return result;
}

json MemoryDataManager::exportTaskData() const {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        json result = json::array();
        for (const auto& data : taskDataList_) {
            result.push_back(data.toJson());
        }
        
        return result;
    } catch (const std::exception& e) {
        LogError("Failed to export task data: " + std::string(e.what()));
        return json::array();
    }
}

json MemoryDataManager::exportJsonResults() const {
    try {
        std::lock_guard<std::mutex> lock(dataMutex_);
        
        json result = json::array();
        for (const auto& jsonResult : jsonResultList_) {
            result.push_back(jsonResult.toJson());
        }
        
        return result;
    } catch (const std::exception& e) {
        LogError("Failed to export JSON results: " + std::string(e.what()));
        return json::array();
    }
}

void MemoryDataManager::updateTaskIdIndex(const std::string& taskId, size_t index) {
    taskIdIndex_[taskId].push_back(index);
}

void MemoryDataManager::updateDllFuncIndex(const std::string& dllName, const std::string& funcName, size_t index) {
    std::string key = generateDllFuncKey(dllName, funcName);
    dllFuncIndex_[key].push_back(index);
}

std::string MemoryDataManager::getCurrentTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::tm tm_buf;
    localtime_s(&tm_buf, &time);
    
    std::stringstream ss;
    ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

std::string MemoryDataManager::generateDllFuncKey(const std::string& dllName, const std::string& funcName) const {
    return dllName + "::" + funcName;
}
