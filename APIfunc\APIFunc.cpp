﻿#include "APIFunc.h"
#include "DLLRouter.h"
#include <Windows.h>
#include <thread>
#include <nlohmann/json.hpp>
#include "../utils/MemoryDataManager.h"
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <atomic>
#include <memory>
#include "../database/TaskData.h"
#include <algorithm>
#include <direct.h>
#include <io.h>
#include <Shlwapi.h>
#include "../utils/Utils.h"
#pragma comment(lib, "Shlwapi.lib")

using json = nlohmann::json;

// WebSocket服务器实例
std::unique_ptr<WebSocketServerImpl> g_wsServer;

// 获取应用程序基础路径
std::string GetBasePath() {
    return Utils::getBasePath();
}

// 获取插件目录路径
std::string GetPluginPath() {
    return Utils::getPluginPath();
}

// 规范化路径 - 移除../和./等特殊路径元素
std::string NormalizePath(const std::string& path) {
    return Utils::normalizePath(path);
}

// 检查路径是否安全 - 确保不会通过../等方式逃离预定目录
bool IsPathSafe(const std::string& path) {
    std::string pluginPath = GetPluginPath();
    return Utils::isPathSafe(path, pluginPath);
}

// 获取安全路径 - 确保路径在plugin目录内
std::string GetSafePath(const std::string& inputPath, const std::string& defaultSubdir) {
    std::string pluginPath = GetPluginPath();
    return Utils::getSafePath(inputPath, pluginPath);
}

// 任务管理
struct TaskInfo {
    std::string taskId;
    bool running;
    std::thread thread;
    std::atomic<bool> stopped{false};
};
std::map<std::string, std::shared_ptr<TaskInfo>> g_tasks;

// 获取当前时间戳
std::string getCurrentTimestamp() {
    return Utils::getCurrentTimestamp();
}

// 生成任务ID
std::string generateTaskId() {
    return Utils::generateUniqueId();
}

// API函数实现
std::string Echo(const std::string& params) {
    return params;
}

std::string GetSystemTime(const std::string& params) {
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    std::tm tm_buf;
    localtime_s(&tm_buf, &now_c);
    std::stringstream ss;
    ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// 通知客户端任务状态更新
void NotifyTaskStatusUpdate(const std::string& taskId, int status, const std::string& result, int progress) {
    if (!g_wsServer) return;

    try {
        json notification;
        notification["type"] = "task_update";
        notification["taskId"] = taskId;
        notification["status"] = status;
        notification["progress"] = progress;

        // 仅当任务已完成(status=2)并且result不为空时，才添加result字段
        if (status == 2 && !result.empty()) {
            notification["result"] = result;
        }

        // 向所有连接的客户端广播任务更新通知
        g_wsServer->Broadcast(notification.dump());
    } catch(const std::exception& e) {
        LogError("通知任务状态更新失败: " + std::string(e.what()));
    }
}

std::string SubmitTask(const std::string& params) {
    try {
        json j = json::parse(params);

        // 验证必要参数
        if(!j.contains("dllName") || !j.contains("funcName")) {
            throw std::runtime_error("Missing required parameters");
        }

        std::string dllName = j["dllName"];
        std::string funcName = j["funcName"];
        std::string taskParams = j.value("params", "");
        int priority = j.value("priority", 0);

        // 提交任务
        std::string taskId = DLLRouter::getInstance().submitTask(
            dllName, funcName, taskParams, priority
        );

        // 通知任务创建
        NotifyTaskStatusUpdate(taskId, 0, "", 0); // 0 表示等待状态

        // 返回任务ID
        json result;
        result["taskId"] = taskId;
        result["status"] = "success";
        return result.dump();

    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

std::string GetTaskStatus(const std::string& params) {
    try {
        json j = json::parse(params);

        if(!j.contains("taskId")) {
            throw std::runtime_error("Missing taskId parameter");
        }

        std::string taskId = j["taskId"];
        auto status = DLLRouter::getInstance().getTaskStatus(taskId);

        json result;
        result["status"] = "success";
        result["taskStatus"] = static_cast<int>(status);
        return result.dump();

    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

std::string GetTaskResult(const std::string& params) {
    try {
        json j = json::parse(params);

        if(!j.contains("taskId")) {
            throw std::runtime_error("Missing taskId parameter");
        }

        std::string taskId = j["taskId"];
        std::string result = DLLRouter::getInstance().getTaskResult(taskId);

        json response;
        response["status"] = "success";
        response["result"] = result;
        return response.dump();

    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

std::string CancelTask(const std::string& params) {
    try {
        json j = json::parse(params);

        if(!j.contains("taskId")) {
            throw std::runtime_error("Missing taskId parameter");
        }

        std::string taskId = j["taskId"];
        bool success = DLLRouter::getInstance().cancelTask(taskId);

        json result;
        result["status"] = success ? "success" : "error";
        if(!success) {
            result["message"] = DLLRouter::getInstance().getLastError();
        }
        return result.dump();

    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

// 注意: 所有DLL管理API已移除
// 现在使用自动DLL发现与加载机制

// WebSocket服务器线程函数
void WebSocketThread() {
    if(g_wsServer) {
                g_wsServer->Run();
    }
}

// API初始化
bool InitializeAPI(int port) {
    try {
        // 确保plugin目录存在
        GetPluginPath();

        // 创建WebSocket服务器实例
        g_wsServer = std::make_unique<WebSocketServerImpl>();

        // 启动服务器
        if(!g_wsServer->Start(port)) {
            return false;
        }

        // 注册API函数
        g_wsServer->RegisterFunction("Echo", [](const std::string& params) { return Echo(params); });
        g_wsServer->RegisterFunction("GetSystemTime", [](const std::string& params) { return GetSystemTime(params); });
        g_wsServer->RegisterFunction("SubmitTask", [](const std::string& params) { return SubmitTask(params); });
        g_wsServer->RegisterFunction("GetTaskStatus", [](const std::string& params) { return GetTaskStatus(params); });
        g_wsServer->RegisterFunction("GetTaskResult", [](const std::string& params) { return GetTaskResult(params); });
        g_wsServer->RegisterFunction("CancelTask", [](const std::string& params) { return CancelTask(params); });
        g_wsServer->RegisterFunction("GetMemoryData", [](const std::string& params) { return GetMemoryData(params); });
        g_wsServer->RegisterFunction("GetMemoryStats", [](const std::string& params) { return GetMemoryStats(params); });

        // 启动WebSocket线程
        std::thread wsThread(WebSocketThread);
        wsThread.detach();

        return true;
    } catch(const std::exception&) {
        return false;
    }
}

// API清理
void CleanupAPI() {
    if (g_wsServer) {
        g_wsServer->Stop();
        g_wsServer.reset();
    }

    // 停止所有任务
    for (auto& task : g_tasks) {
        task.second->stopped = true;
        if (task.second->thread.joinable()) {
            task.second->thread.join();
        }
    }
    g_tasks.clear();

}

std::string GetMemoryData(const std::string& params) {
    try {
        json j = json::parse(params);

        std::string dataType = j.value("type", "all"); // all, tasks, json
        int limit = j.value("limit", 100);

        json result;
        result["status"] = "success";

        if (dataType == "all" || dataType == "tasks") {
            auto taskData = MemoryDataManager::getInstance().getLatestTaskData(limit);
            json taskArray = json::array();
            for (const auto& task : taskData) {
                taskArray.push_back(task.toJson());
            }
            result["taskData"] = taskArray;
        }

        if (dataType == "all" || dataType == "json") {
            auto jsonResults = MemoryDataManager::getInstance().getJsonResults("", "", limit);
            json jsonArray = json::array();
            for (const auto& jsonResult : jsonResults) {
                jsonArray.push_back(jsonResult.toJson());
            }
            result["jsonResults"] = jsonArray;
        }

        return result.dump();
    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

std::string GetMemoryStats(const std::string& params) {
    try {
        json result;
        result["status"] = "success";
        result["taskDataCount"] = MemoryDataManager::getInstance().getTaskDataCount();
        result["jsonResultCount"] = MemoryDataManager::getInstance().getJsonResultCount();

        // 获取内存使用情况（简单统计）
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        std::tm tm_buf;
        localtime_s(&tm_buf, &time);
        std::stringstream ss;
        ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
        result["timestamp"] = ss.str();

        return result.dump();
    } catch(const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}