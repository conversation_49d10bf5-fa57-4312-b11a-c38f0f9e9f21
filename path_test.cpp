#include <iostream>
#include <string>
#include <Windows.h>
#include <vector>

// 简化的路径处理函数测试
std::string wideToUtf8(const std::wstring& wideStr) {
    if (wideStr.empty()) {
        return std::string();
    }

    int utf8Len = WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, NULL, 0, NULL, NULL);
    if (utf8Len <= 0) {
        return std::string();
    }

    std::vector<char> utf8Buffer(utf8Len);
    WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, utf8Buffer.data(), utf8Len, NULL, NULL);
    
    return std::string(utf8Buffer.data());
}

std::wstring utf8ToWide(const std::string& utf8Str) {
    if (utf8Str.empty()) {
        return std::wstring();
    }

    int wideLen = MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, NULL, 0);
    if (wideLen <= 0) {
        return std::wstring();
    }

    std::vector<wchar_t> wideBuffer(wideLen);
    MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, wideBuffer.data(), wideLen);
    
    return std::wstring(wideBuffer.data());
}

std::string getBasePathUtf8() {
    wchar_t modulePath[MAX_PATH];
    GetModuleFileNameW(NULL, modulePath, MAX_PATH);
    std::wstring wideExePath(modulePath);
    std::string utf8ExePath = wideToUtf8(wideExePath);
    return utf8ExePath.substr(0, utf8ExePath.find_last_of("\\") + 1);
}

int main() {
    std::cout << "=== 中文路径处理测试 ===" << std::endl;
    
    // 测试路径获取
    std::string basePath = getBasePathUtf8();
    std::cout << "UTF-8基础路径: " << basePath << std::endl;
    
    // 测试中文字符串转换
    std::wstring testChinese = L"测试中文路径\\数据库\\tasks.db";
    std::string utf8Path = wideToUtf8(testChinese);
    std::wstring backToWide = utf8ToWide(utf8Path);
    
    std::cout << "UTF-8中文路径: " << utf8Path << std::endl;
    std::wcout << L"转换回宽字符: " << backToWide << std::endl;
    
    // 测试完整路径
    std::string fullPath = basePath + utf8Path;
    std::cout << "完整UTF-8路径: " << fullPath << std::endl;
    
    // 测试目录创建
    std::wstring wideFullPath = utf8ToWide(fullPath);
    std::wstring wideDir = wideFullPath.substr(0, wideFullPath.find_last_of(L"\\"));
    
    std::wcout << L"要创建的目录: " << wideDir << std::endl;
    
    if (_waccess(wideDir.c_str(), 0) != 0) {
        std::cout << "目录不存在，尝试创建..." << std::endl;
        if (_wmkdir(wideDir.c_str()) == 0) {
            std::cout << "目录创建成功！" << std::endl;
        } else {
            std::cout << "目录创建失败，错误代码: " << GetLastError() << std::endl;
        }
    } else {
        std::cout << "目录已存在" << std::endl;
    }
    
    return 0;
}
