﻿#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <chrono>
#include "../database/TaskData.h"
#include "nlohmann/json.hpp"

using json = nlohmann::json;

// 内存中的任务数据结构
struct MemoryTaskData {
    std::string taskId;
    std::string dllName;
    std::string funcName;
    std::string dataType;
    std::string content;
    int progress;
    std::string timestamp;
    std::string params;
    
    // 转换为JSON
    json toJson() const {
        json j;
        j["taskId"] = taskId;
        j["dllName"] = dllName;
        j["funcName"] = funcName;
        j["dataType"] = dataType;
        j["content"] = content;
        j["progress"] = progress;
        j["timestamp"] = timestamp;
        j["params"] = params;
        return j;
    }
    
    // 从TaskData转换
    static MemoryTaskData fromTaskData(const TaskData& data) {
        MemoryTaskData memData;
        memData.taskId = data.taskId;
        memData.dllName = data.dllName;
        memData.funcName = data.funcName;
        memData.dataType = data.dataType;
        memData.content = data.content;
        memData.progress = data.progress;
        memData.timestamp = data.timestamp;
        memData.params = data.params;
        return memData;
    }
};

// 内存中的JSON处理结果
struct MemoryJsonResult {
    std::string taskId;
    std::string dllName;
    std::string funcName;
    json jsonData;
    std::string timestamp;
    std::string params;
    
    json toJson() const {
        json j;
        j["taskId"] = taskId;
        j["dllName"] = dllName;
        j["funcName"] = funcName;
        j["data"] = jsonData;
        j["timestamp"] = timestamp;
        j["params"] = params;
        return j;
    }
};

// 内存数据管理器
class MemoryDataManager {
public:
    static MemoryDataManager& getInstance();
    
    // 初始化
    bool initialize();
    void cleanup();
    
    // 任务数据操作
    bool insertTaskData(const TaskData& data);
    bool insertTaskData(const MemoryTaskData& data);
    std::vector<MemoryTaskData> getLatestTaskData(int limit = 100);
    std::vector<MemoryTaskData> getTaskDataByTaskId(const std::string& taskId, const std::string& dataType = "");
    MemoryTaskData getTaskResultByTaskId(const std::string& taskId);
    
    // JSON结果操作
    bool insertJsonResult(const std::string& taskId, const std::string& dllName, 
                         const std::string& funcName, const json& jsonData, 
                         const std::string& params = "");
    std::vector<MemoryJsonResult> getJsonResults(const std::string& dllName = "", 
                                               const std::string& funcName = "", 
                                               int limit = 100);
    MemoryJsonResult getJsonResultByTaskId(const std::string& taskId);
    
    // 批量操作
    bool batchInsertTaskData(const std::vector<TaskData>& dataList);
    bool batchInsertTaskData(const std::vector<MemoryTaskData>& dataList);
    
    // 查询操作
    std::vector<MemoryTaskData> getResultsByDllAndFunc(const std::string& dllName, 
                                                      const std::string& funcName, 
                                                      int limit = 100);
    
    // 数据清理
    bool cleanupOldData(int minutesToKeep = 60); // 改为分钟，内存数据不需要保存太久
    
    // 统计信息
    size_t getTaskDataCount() const;
    size_t getJsonResultCount() const;
    
    // 导出功能（可选，用于调试或备份）
    json exportAllData() const;
    json exportTaskData() const;
    json exportJsonResults() const;
    
    // 获取最后一次错误
    std::string getLastError() const { return lastError_; }

private:
    MemoryDataManager() = default;
    ~MemoryDataManager() = default;
    MemoryDataManager(const MemoryDataManager&) = delete;
    MemoryDataManager& operator=(const MemoryDataManager&) = delete;
    
    // 数据存储
    std::vector<MemoryTaskData> taskDataList_;
    std::vector<MemoryJsonResult> jsonResultList_;
    
    // 索引映射（提高查询效率）
    std::unordered_map<std::string, std::vector<size_t>> taskIdIndex_;
    std::unordered_map<std::string, std::vector<size_t>> dllFuncIndex_;
    
    // 线程安全
    mutable std::mutex dataMutex_;
    
    // 错误处理
    std::string lastError_;
    void setError(const std::string& error) {
        lastError_ = error;
    }
    
    // 辅助函数
    void updateTaskIdIndex(const std::string& taskId, size_t index);
    void updateDllFuncIndex(const std::string& dllName, const std::string& funcName, size_t index);
    std::string getCurrentTimestamp() const;
    std::string generateDllFuncKey(const std::string& dllName, const std::string& funcName) const;
};
