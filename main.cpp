﻿#include "mbwindow/mb_window.h"
#include "APIFunc/APIFunc.h"
#include "WebSocket/WebSocketServer.h"
#include "APIfunc/DLLRouter.h"
#include "utils/Utils.h"
#include "utils/MemoryDataManager.h"
#include <direct.h> // for _mkdir
#include <io.h> // for _access
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>


// 全局变量
std::atomic<bool> g_isRunning(true);  // 控制程序运行状态
std::thread g_backendThread;          // 后端处理线程
std::mutex g_backendMutex;            // 后端线程互斥锁
std::condition_variable g_backendCV;  // 后端线程条件变量
HANDLE g_singleInstanceMutex = NULL;  // 单实例互斥锁

// 函数声明
void BackendThreadFunc();
bool CheckSingleInstance();
void ActivateExistingWindow();
void ReleaseSingleInstanceMutex();

// 单实例检查函数
bool CheckSingleInstance() {
    // 创建命名互斥锁
    g_singleInstanceMutex = CreateMutexW(NULL, FALSE, L"TaiZhangSystem_SingleInstance_Mutex");

    if (g_singleInstanceMutex == NULL) {
        // 创建互斥锁失败
        MessageBoxW(NULL, L"无法创建单实例检查互斥锁", L"错误", MB_OK | MB_ICONERROR);
        return false;
    }

    // 检查是否已有实例在运行
    DWORD dwError = GetLastError();
    if (dwError == ERROR_ALREADY_EXISTS) {
        // 已有实例在运行
        return false;
    }

    // 当前是唯一实例
    return true;
}

// 激活已存在的窗口
void ActivateExistingWindow() {
    // 查找已存在的窗口
    HWND hExistingWnd = FindWindowW(kClassWindow, NULL);

    if (hExistingWnd != NULL) {
        // 如果窗口被最小化，先恢复
        if (IsIconic(hExistingWnd)) {
            ShowWindow(hExistingWnd, SW_RESTORE);
        }

        // 将窗口带到前台
        SetForegroundWindow(hExistingWnd);
        BringWindowToTop(hExistingWnd);

        // 确保窗口可见
        ShowWindow(hExistingWnd, SW_SHOW);
    }
}

// 释放单实例互斥锁
void ReleaseSingleInstanceMutex() {
    if (g_singleInstanceMutex != NULL) {
        CloseHandle(g_singleInstanceMutex);
        g_singleInstanceMutex = NULL;
    }
}

// 后端线程函数实现
void BackendThreadFunc() {
    try {
        // 初始化API和WebSocket服务器
        if (!InitializeAPI(9002)) {
            MessageBoxW(NULL, L"后端服务初始化失败", L"错误", MB_OK | MB_ICONERROR);
            return;
        }

        // 通知主线程后端已初始化完成
        {
            std::lock_guard<std::mutex> lock(g_backendMutex);
            g_backendCV.notify_one();
        }

        // 后端处理循环
        while (g_isRunning) {
            // 这里可以添加定期需要执行的后端任务
            // 例如：检查长时间运行的任务、清理过期数据等

            // 使用条件变量等待，可以被主线程唤醒或超时
            {
                std::unique_lock<std::mutex> lock(g_backendMutex);
                g_backendCV.wait_for(lock, std::chrono::milliseconds(100));
            }
        }

        // 清理后端资源
        // 清理API资源
        CleanupAPI();
    }
    catch (const std::exception& e) {
        // 即使发生异常，也尝试清理API资源
        try {
            CleanupAPI();
        } catch (...) {
            // 清理失败，静默处理
        }
    }
    catch (...) {
        // 即使发生异常，也尝试清理API资源
        try {
            CleanupAPI();
        } catch (...) {
            // 清理失败，静默处理
        }
    }
}


// 程序入口点
int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
    _In_opt_ HINSTANCE hPrevInstance,
    _In_ LPWSTR    lpCmdLine,
    _In_ int       nCmdShow)
{
    // 首先检查单实例
    if (!CheckSingleInstance()) {
        // 已有实例在运行，尝试激活已存在的窗口
        ActivateExistingWindow();

        // 显示友好提示
        MessageBoxW(NULL, L"台账系统已经打开，请查看任务栏或桌面上的窗口。", L"提示", MB_OK | MB_ICONINFORMATION);

        // 释放互斥锁并退出
        ReleaseSingleInstanceMutex();
        return 0;
    }

    try {

        // 获取应用程序路径（使用UTF-8编码安全的方式）
        std::string baseDir = Utils::getBasePathUtf8();
        // 移除末尾的反斜杠（如果存在）
        if (!baseDir.empty() && baseDir.back() == '\\') {
            baseDir.pop_back();
        }

        // 初始化内存数据管理器（替代外部数据库文件）
        if (!MemoryDataManager::getInstance().initialize()) {
            MessageBoxW(NULL, L"内存数据管理器初始化失败", L"错误", MB_OK | MB_ICONERROR);
            return 1;
        }

        // 注意：JsonSchemaProcessor已禁用，因为我们使用内存存储而不是数据库表

        // 初始化Winsock
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            MessageBoxW(NULL, L"Winsock初始化失败", L"错误", MB_OK | MB_ICONERROR);
            return 1;
        }
    } catch (const std::exception& e) {
        MessageBoxA(NULL, ("Initialization error: " + std::string(e.what())).c_str(), "Error", MB_OK | MB_ICONERROR);
        return 1;
    } catch (...) {
        MessageBoxW(NULL, L"初始化过程中发生未知错误", L"错误", MB_OK | MB_ICONERROR);
        return 1;
    }

    // 初始化miniblink
    mbSettings settings;
    memset(&settings, 0, sizeof(settings));
    mbInit(&settings);

    // 启动后端线程
    g_backendThread = std::thread(BackendThreadFunc);

    // 等待后端初始化完成
    {
        std::unique_lock<std::mutex> lock(g_backendMutex);
        g_backendCV.wait_for(lock, std::chrono::seconds(5)); // 最多等待5秒
    }

    // 创建窗口并初始化
    createSimpleMb();

    // 优化的消息循环
    MSG msg = { 0 };
    DWORD lastWakeTime = GetTickCount();
    const DWORD WAKE_INTERVAL = 16; // 约60FPS

    while (g_isRunning) {
        // 处理所有待处理的Windows消息
        while (::PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            if (WM_QUIT == msg.message) {
                g_isRunning = false;
                break;
            }
            ::TranslateMessage(&msg);
            ::DispatchMessageW(&msg);
        }

        // 控制mbWake的调用频率，避免过度唤醒
        DWORD currentTime = GetTickCount();
        if (currentTime - lastWakeTime >= WAKE_INTERVAL) {
            ::mbWake(NULL_WEBVIEW);
            lastWakeTime = currentTime;
        }
        else {
            // 短暂休眠，减少CPU使用
            Sleep(1);
        }
    }

    // 通知后端线程退出
    {
        std::lock_guard<std::mutex> lock(g_backendMutex);
        g_isRunning = false;
        g_backendCV.notify_one();
    }

    // 等待后端线程结束 - 后端线程负责清理API资源
    if (g_backendThread.joinable()) {
        g_backendThread.join();
    }

    try {
        // 清理资源

        // 清理内存数据管理器
        MemoryDataManager::getInstance().cleanup();

        // 清理miniblink
        mbUninit();

        // 清理Winsock
        WSACleanup();

        // 释放单实例互斥锁
        ReleaseSingleInstanceMutex();

        return 0;
    } catch (const std::exception& e) {
        // 确保在异常情况下也释放互斥锁
        ReleaseSingleInstanceMutex();
        return 1;
    } catch (...) {
        // 确保在异常情况下也释放互斥锁
        ReleaseSingleInstanceMutex();
        return 1;
    }
}