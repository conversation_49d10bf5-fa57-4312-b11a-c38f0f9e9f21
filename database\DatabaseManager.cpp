#include "DatabaseManager.h"
#include <sstream>
#include <chrono>
#include <iomanip>
#include <direct.h> // for _mkdir
#include <io.h> // for _access
#include "../utils/Utils.h"

DatabaseManager& DatabaseManager::getInstance() {
    static DatabaseManager instance;
    return instance;
}

bool DatabaseManager::initialize(const std::string& dbPath) {
    try {
        // 确保数据库目录存在（使用UTF-8编码安全的方式）
        Utils::ensureDirectoryExistsUtf8(dbPath);

        LogInfo("Initializing database: " + dbPath);

        if (sqlite3_open(dbPath.c_str(), &m_db) != SQLITE_OK) {
            lastError_ = "Failed to open database: " + std::string(sqlite3_errmsg(m_db));
            LogError(lastError_);
            return false;
        }

        // 设置数据库编码为UTF-8
        executeQuery("PRAGMA encoding = 'UTF-8';");
        LogInfo("Database encoding set to UTF-8");

        // 启用外键约束
        executeQuery("PRAGMA foreign_keys = ON;");

        // 创建表
        if (!createTables()) {
            LogError("Failed to create tables");
            return false;
        }

        LogInfo("Database initialized successfully");
        return true;
    } catch (const std::exception& e) {
        lastError_ = "Exception during database initialization: " + std::string(e.what());
        LogError(lastError_);
        return false;
    } catch (...) {
        lastError_ = "Unknown exception during database initialization";
        LogError(lastError_);
        return false;
    }
}

void DatabaseManager::cleanup() {
    try {
        LogInfo("Cleaning up database resources");

        // 释放所有预编译语句
        for (auto& pair : preparedStatements_) {
            if (pair.second) {
                sqlite3_finalize(pair.second);
                pair.second = nullptr;
            }
        }
        preparedStatements_.clear();

        // 关闭数据库连接
        if (m_db) {
            sqlite3_close(m_db);
            m_db = nullptr;
            LogInfo("Database connection closed");
        }
    } catch (const std::exception& e) {
        lastError_ = "Exception during database cleanup: " + std::string(e.what());
        LogError(lastError_);
    } catch (...) {
        lastError_ = "Unknown exception during database cleanup";
        LogError(lastError_);
    }
}

bool DatabaseManager::createTables() {
    // 首先检查表是否存在
    bool tableExists = false;
    {
        const char* checkSql = "SELECT name FROM sqlite_master WHERE type='table' AND name='task_data';";
        sqlite3_stmt* stmt;
        if (sqlite3_prepare_v2(m_db, checkSql, -1, &stmt, nullptr) == SQLITE_OK) {
            if (sqlite3_step(stmt) == SQLITE_ROW) {
                tableExists = true;
            }
            sqlite3_finalize(stmt);
        }
    }

    if (!tableExists) {
        // 如果表不存在，创建新表结构
        const char* createSql = R"(
            CREATE TABLE IF NOT EXISTS task_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                dll_name TEXT,
                func_name TEXT,
                data_type TEXT NOT NULL,
                content TEXT NOT NULL,
                progress INTEGER NOT NULL,
                timestamp TEXT NOT NULL,
                params TEXT
            );
            CREATE INDEX IF NOT EXISTS idx_task_id ON task_data(task_id);
            CREATE INDEX IF NOT EXISTS idx_dll_func ON task_data(dll_name, func_name);
            CREATE INDEX IF NOT EXISTS idx_timestamp ON task_data(timestamp);
        )";

        return executeQuery(createSql);
    } else {
        // 如果表已存在，检查是否需要添加新列
        bool hasAllColumns = false;
        {
            const char* checkColSql = "PRAGMA table_info(task_data);";
            sqlite3_stmt* stmt;
            if (sqlite3_prepare_v2(m_db, checkColSql, -1, &stmt, nullptr) == SQLITE_OK) {
                int dllNameCol = 0;
                int funcNameCol = 0;
                int paramsCol = 0;

                while (sqlite3_step(stmt) == SQLITE_ROW) {
                    const char* colName = (const char*)sqlite3_column_text(stmt, 1);
                    if (strcmp(colName, "dll_name") == 0) dllNameCol++;
                    if (strcmp(colName, "func_name") == 0) funcNameCol++;
                    if (strcmp(colName, "params") == 0) paramsCol++;
                }

                hasAllColumns = (dllNameCol > 0 && funcNameCol > 0 && paramsCol > 0);
                sqlite3_finalize(stmt);
            }
        }

        if (!hasAllColumns) {
            // 检查具体缺失哪些列
            bool hasDllName = false;
            bool hasFuncName = false;
            bool hasParams = false;

            {
                const char* checkColSql = "PRAGMA table_info(task_data);";
                sqlite3_stmt* stmt;
                if (sqlite3_prepare_v2(m_db, checkColSql, -1, &stmt, nullptr) == SQLITE_OK) {
                    while (sqlite3_step(stmt) == SQLITE_ROW) {
                        const char* colName = (const char*)sqlite3_column_text(stmt, 1);
                        if (strcmp(colName, "dll_name") == 0) hasDllName = true;
                        if (strcmp(colName, "func_name") == 0) hasFuncName = true;
                        if (strcmp(colName, "params") == 0) hasParams = true;
                    }
                    sqlite3_finalize(stmt);
                }
            }

            // 添加缺失的列
            if (!hasDllName) {
                if (!executeQuery("ALTER TABLE task_data ADD COLUMN dll_name TEXT;")) {
                    return false;
                }
            }

            if (!hasFuncName) {
                if (!executeQuery("ALTER TABLE task_data ADD COLUMN func_name TEXT;")) {
                    return false;
                }
            }

            if (!hasParams) {
                if (!executeQuery("ALTER TABLE task_data ADD COLUMN params TEXT;")) {
                    return false;
                }
            }

            // 创建索引
            if (!executeQuery("CREATE INDEX IF NOT EXISTS idx_dll_func ON task_data(dll_name, func_name);")) {
                return false;
            }
        }

        return true;
    }
}

bool DatabaseManager::insertTaskData(const TaskData& data) {
    const char* sql = R"(
        INSERT INTO task_data (task_id, dll_name, func_name, data_type, content, progress, timestamp, params)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?);
    )";

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(m_db, sql, -1, &stmt, nullptr) != SQLITE_OK) {
        return false;
    }

    // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，确保字符串被复制并正确处理中文
    sqlite3_bind_text(stmt, 1, data.taskId.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_text(stmt, 2, data.dllName.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_text(stmt, 3, data.funcName.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_text(stmt, 4, data.dataType.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_text(stmt, 5, data.content.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_int(stmt, 6, data.progress);
    sqlite3_bind_text(stmt, 7, data.timestamp.c_str(), -1, SQLITE_TRANSIENT);
    sqlite3_bind_text(stmt, 8, data.params.c_str(), -1, SQLITE_TRANSIENT);

    bool result = sqlite3_step(stmt) == SQLITE_DONE;
    sqlite3_finalize(stmt);

    return result;
}

std::vector<TaskData> DatabaseManager::getLatestTaskData(int limit) {
    std::vector<TaskData> results;
    const char* sql = R"(
        SELECT task_id, dll_name, func_name, data_type, content, progress, timestamp, params
        FROM task_data
        ORDER BY timestamp DESC
        LIMIT ?;
    )";

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(m_db, sql, -1, &stmt, nullptr) != SQLITE_OK) {
        return results;
    }

    sqlite3_bind_int(stmt, 1, limit);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        TaskData data;
        data.taskId = (const char*)sqlite3_column_text(stmt, 0);

        // 处理可能为空的字段
        if (sqlite3_column_text(stmt, 1) != nullptr) {
            data.dllName = (const char*)sqlite3_column_text(stmt, 1);
        }
        if (sqlite3_column_text(stmt, 2) != nullptr) {
            data.funcName = (const char*)sqlite3_column_text(stmt, 2);
        }

        data.dataType = (const char*)sqlite3_column_text(stmt, 3);
        data.content = (const char*)sqlite3_column_text(stmt, 4);
        data.progress = sqlite3_column_int(stmt, 5);
        data.timestamp = (const char*)sqlite3_column_text(stmt, 6);

        // 处理可能为空的params字段
        if (sqlite3_column_text(stmt, 7) != nullptr) {
            data.params = (const char*)sqlite3_column_text(stmt, 7);
        }

        results.push_back(data);
    }

    sqlite3_finalize(stmt);
    return results;
}

std::vector<TaskData> DatabaseManager::getTaskDataByTaskId(const std::string& taskId, const std::string& dataType) {
    std::vector<TaskData> results;
    std::string sql;

    // 根据是否指定数据类型构造不同的SQL查询
    if (dataType.empty()) {
        sql = R"(
            SELECT task_id, dll_name, func_name, data_type, content, progress, timestamp, params
            FROM task_data
            WHERE task_id = ?
            ORDER BY timestamp DESC;
        )";
    } else {
        sql = R"(
            SELECT task_id, dll_name, func_name, data_type, content, progress, timestamp, params
            FROM task_data
            WHERE task_id = ? AND data_type = ?
            ORDER BY timestamp DESC;
        )";
    }

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
        return results;
    }

    sqlite3_bind_text(stmt, 1, taskId.c_str(), -1, SQLITE_STATIC);
    if (!dataType.empty()) {
        sqlite3_bind_text(stmt, 2, dataType.c_str(), -1, SQLITE_STATIC);
    }

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        TaskData data;
        data.taskId = (const char*)sqlite3_column_text(stmt, 0);

        // 处理可能为空的字段
        if (sqlite3_column_text(stmt, 1) != nullptr) {
            data.dllName = (const char*)sqlite3_column_text(stmt, 1);
        }
        if (sqlite3_column_text(stmt, 2) != nullptr) {
            data.funcName = (const char*)sqlite3_column_text(stmt, 2);
        }

        data.dataType = (const char*)sqlite3_column_text(stmt, 3);
        data.content = (const char*)sqlite3_column_text(stmt, 4);
        data.progress = sqlite3_column_int(stmt, 5);
        data.timestamp = (const char*)sqlite3_column_text(stmt, 6);

        // 处理可能为空的params字段
        if (sqlite3_column_text(stmt, 7) != nullptr) {
            data.params = (const char*)sqlite3_column_text(stmt, 7);
        }

        results.push_back(data);
    }

    sqlite3_finalize(stmt);
    return results;
}

bool DatabaseManager::executeQuery(const std::string& sql) {
    try {
        char* errMsg = nullptr;
        int rc = sqlite3_exec(m_db, sql.c_str(), nullptr, nullptr, &errMsg);

        if (rc != SQLITE_OK) {
            lastError_ = "SQL error: " + std::string(errMsg ? errMsg : "unknown error");
            LogError(lastError_ + ", SQL: " + sql);

            if (errMsg) {
                sqlite3_free(errMsg);
            }
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        lastError_ = "Exception during query execution: " + std::string(e.what());
        LogError(lastError_ + ", SQL: " + sql);
        return false;
    } catch (...) {
        lastError_ = "Unknown exception during query execution";
        LogError(lastError_ + ", SQL: " + sql);
        return false;
    }
}

// 按DLL名称和函数名查询结果数据
std::vector<TaskData> DatabaseManager::getResultsByDllAndFunc(const std::string& dllName, const std::string& funcName, int limit) {
    std::vector<TaskData> results;
    const char* sql = R"(
        SELECT task_id, dll_name, func_name, data_type, content, progress, timestamp, params
        FROM task_data
        WHERE dll_name = ? AND func_name = ? AND data_type = 'result'
        ORDER BY timestamp DESC
        LIMIT ?;
    )";

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(m_db, sql, -1, &stmt, nullptr) != SQLITE_OK) {
        return results;
    }

    sqlite3_bind_text(stmt, 1, dllName.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, funcName.c_str(), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 3, limit);

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        TaskData data;
        data.taskId = (const char*)sqlite3_column_text(stmt, 0);

        if (sqlite3_column_text(stmt, 1) != nullptr) {
            data.dllName = (const char*)sqlite3_column_text(stmt, 1);
        }
        if (sqlite3_column_text(stmt, 2) != nullptr) {
            data.funcName = (const char*)sqlite3_column_text(stmt, 2);
        }

        data.dataType = (const char*)sqlite3_column_text(stmt, 3);
        data.content = (const char*)sqlite3_column_text(stmt, 4);
        data.progress = sqlite3_column_int(stmt, 5);
        data.timestamp = (const char*)sqlite3_column_text(stmt, 6);

        // 处理可能为空的params字段
        if (sqlite3_column_text(stmt, 7) != nullptr) {
            data.params = (const char*)sqlite3_column_text(stmt, 7);
        }

        results.push_back(data);
    }

    sqlite3_finalize(stmt);
    return results;
}

// 获取特定任务ID的结果数据
TaskData DatabaseManager::getTaskResultByTaskId(const std::string& taskId) {
    TaskData result;
    const char* sql = R"(
        SELECT task_id, dll_name, func_name, data_type, content, progress, timestamp, params
        FROM task_data
        WHERE task_id = ? AND data_type = 'result'
        LIMIT 1;
    )";

    sqlite3_stmt* stmt;
    if (sqlite3_prepare_v2(m_db, sql, -1, &stmt, nullptr) != SQLITE_OK) {
        return result;
    }

    sqlite3_bind_text(stmt, 1, taskId.c_str(), -1, SQLITE_STATIC);

    if (sqlite3_step(stmt) == SQLITE_ROW) {
        result.taskId = (const char*)sqlite3_column_text(stmt, 0);

        if (sqlite3_column_text(stmt, 1) != nullptr) {
            result.dllName = (const char*)sqlite3_column_text(stmt, 1);
        }
        if (sqlite3_column_text(stmt, 2) != nullptr) {
            result.funcName = (const char*)sqlite3_column_text(stmt, 2);
        }

        result.dataType = (const char*)sqlite3_column_text(stmt, 3);
        result.content = (const char*)sqlite3_column_text(stmt, 4);
        result.progress = sqlite3_column_int(stmt, 5);
        result.timestamp = (const char*)sqlite3_column_text(stmt, 6);

        // 处理可能为空的params字段
        if (sqlite3_column_text(stmt, 7) != nullptr) {
            result.params = (const char*)sqlite3_column_text(stmt, 7);
        }
    }

    sqlite3_finalize(stmt);
    return result;
}

// 获取当前时间戳
std::string DatabaseManager::getCurrentTimestamp() {
    return Utils::getCurrentTimestamp();
}

// 预编译语句
sqlite3_stmt* DatabaseManager::prepareStatement(const std::string& sql) {
    try {
        // 检查缓存
        auto it = preparedStatements_.find(sql);
        if (it != preparedStatements_.end() && it->second != nullptr) {
            sqlite3_reset(it->second);
            sqlite3_clear_bindings(it->second);
            return it->second;
        }

        // 创建新的预编译语句
        sqlite3_stmt* stmt = nullptr;
        int rc = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);

        if (rc != SQLITE_OK) {
            lastError_ = "Failed to prepare statement: " + std::string(sqlite3_errmsg(m_db));
            LogError(lastError_ + ", SQL: " + sql);
            return nullptr;
        }

        // 存入缓存
        preparedStatements_[sql] = stmt;
        return stmt;
    } catch (const std::exception& e) {
        lastError_ = "Exception during statement preparation: " + std::string(e.what());
        LogError(lastError_ + ", SQL: " + sql);
        return nullptr;
    } catch (...) {
        lastError_ = "Unknown exception during statement preparation";
        LogError(lastError_ + ", SQL: " + sql);
        return nullptr;
    }
}

// 批量插入任务数据
bool DatabaseManager::batchInsertTaskData(const std::vector<TaskData>& dataList) {
    if (dataList.empty()) {
        return true;
    }

    try {
        LogInfo("Batch inserting " + std::to_string(dataList.size()) + " task data records");

        // 开始事务
        if (!executeQuery("BEGIN TRANSACTION;")) {
            LogError("Failed to begin transaction");
            return false;
        }

        const char* sql = "INSERT INTO task_data (task_id, dll_name, func_name, data_type, content, progress, timestamp, params) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        sqlite3_stmt* stmt = prepareStatement(sql);
        if (!stmt) {
            executeQuery("ROLLBACK;");
            return false;
        }

        bool success = true;
        for (const auto& data : dataList) {
            sqlite3_reset(stmt);
            sqlite3_clear_bindings(stmt);

            // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，确保字符串被复制并正确处理中文
            sqlite3_bind_text(stmt, 1, data.taskId.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 2, data.dllName.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 3, data.funcName.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 4, data.dataType.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 5, data.content.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_int(stmt, 6, data.progress);
            sqlite3_bind_text(stmt, 7, data.timestamp.c_str(), -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 8, data.params.c_str(), -1, SQLITE_TRANSIENT);

            if (sqlite3_step(stmt) != SQLITE_DONE) {
                lastError_ = "Failed to insert data: " + std::string(sqlite3_errmsg(m_db));
                LogError(lastError_);
                success = false;
                break;
            }
        }

        // 提交或回滚事务
        if (success) {
            if (!executeQuery("COMMIT;")) {
                LogError("Failed to commit transaction");
                return false;
            }
            LogInfo("Successfully inserted " + std::to_string(dataList.size()) + " records");
            return true;
        } else {
            executeQuery("ROLLBACK;");
            LogWarning("Transaction rolled back");
            return false;
        }
    } catch (const std::exception& e) {
        lastError_ = "Exception during batch insert: " + std::string(e.what());
        LogError(lastError_);
        executeQuery("ROLLBACK;");
        return false;
    } catch (...) {
        lastError_ = "Unknown exception during batch insert";
        LogError(lastError_);
        executeQuery("ROLLBACK;");
        return false;
    }
}

// 清理旧数据
bool DatabaseManager::cleanupOldData(int daysToKeep) {
    try {
        LogInfo("Cleaning up old data, keeping data for " + std::to_string(daysToKeep) + " days");

        // 计算截止日期
        auto now = std::chrono::system_clock::now();
        auto cutoff = now - std::chrono::hours(24 * daysToKeep);
        auto time = std::chrono::system_clock::to_time_t(cutoff);

        std::tm tm_buf;
        localtime_s(&tm_buf, &time);

        std::stringstream ss;
        ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
        std::string cutoffDate = ss.str();

        // 执行清理
        const char* sql = "DELETE FROM task_data WHERE timestamp < ?;";

        sqlite3_stmt* stmt = prepareStatement(sql);
        if (!stmt) {
            return false;
        }

        sqlite3_bind_text(stmt, 1, cutoffDate.c_str(), -1, SQLITE_STATIC);

        if (sqlite3_step(stmt) != SQLITE_DONE) {
            lastError_ = "Failed to execute cleanup: " + std::string(sqlite3_errmsg(m_db));
            LogError(lastError_);
            return false;
        }

        int deleted = sqlite3_changes(m_db);
        LogInfo("Cleaned up " + std::to_string(deleted) + " old records");

        return true;
    } catch (const std::exception& e) {
        lastError_ = "Exception during data cleanup: " + std::string(e.what());
        LogError(lastError_);
        return false;
    } catch (...) {
        lastError_ = "Unknown exception during data cleanup";
        LogError(lastError_);
        return false;
    }
}
