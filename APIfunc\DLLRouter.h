﻿#pragma once
#include <Windows.h>
#include <string>
#include <unordered_map>
#include <functional>
#include <memory>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <vector>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include "../database/DatabaseManager.h"
#include "../database/TaskData.h"
#include "../database/JsonSchemaProcessor.h"
#include "../utils/Logger.h"
#include "../utils/Exceptions.h"

// 定义进度回调函数类型
typedef void (*ProgressCallback)(const std::string& taskId, int progress);

// 使用nlohmann/json库
using json = nlohmann::json;

// DLL清单文件结构
struct DllManifest {
    std::string name;
    std::string version;
    std::string description;
    std::vector<std::string> functions;
};

class DLLRouter {
public:
    // 任务状态枚举
    enum class TaskStatus {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED,
        CANCELED
    };

    // 任务结构体
    struct Task {
        std::string taskId;
        std::string dllName;
        std::string funcName;
        std::string params;
        TaskStatus status;
        std::string result;
        int priority;
        int progress; // 任务进度，0-100

        Task() : status(TaskStatus::PENDING), priority(0), progress(0) {}
    };

    // 进度更新结构体
    struct ProgressUpdate {
        std::string taskId;
        int progress;

        ProgressUpdate(const std::string& id, int prog)
            : taskId(id), progress(prog) {}
    };

    // 任务比较器 - 移到前面来先声明
    struct TaskComparator {
        bool operator()(const Task* a, const Task* b) const {
            // 优先级越小，优先级越高
            return a->priority > b->priority;
        }
    };

    static DLLRouter& getInstance() {
        static DLLRouter instance;
        return instance;
    }

    // 注册进度回调函数
    void registerProgressCallback(ProgressCallback callback);

    // 自动DLL发现与加载
    bool scanAndLoadPlugins(const std::string& pluginDir = "plugin");

    // 获取插件目录路径
    std::string GetPluginPath();

    // 注意: 所有向后兼容的DLL管理方法已移除
    // 现在只使用自动DLL发现与加载机制

    // 同步执行
    std::string executeFunction(const std::string& name, const std::string& params);

    // 异步执行
    std::string submitTask(const std::string& dllName,
                          const std::string& funcName,
                          const std::string& params,
                          int priority = 0);

    // 任务控制
    bool cancelTask(const std::string& taskId);
    TaskStatus getTaskStatus(const std::string& taskId);
    std::string getTaskResult(const std::string& taskId);
    int getTaskProgress(const std::string& taskId);

    // 更新任务进度 (同步方法，现主要用于内部实际更新)
    void updateTaskProgress(const std::string& taskId, int progress);

    // 异步进度更新 (新增)
    void queueProgressUpdate(const std::string& taskId, int progress);

    // 错误处理
    std::string getLastError() const { return lastError_; }

    // 日志级别控制
    void setLogLevel(LogLevel level);

    // 数据清理
    bool cleanupOldData(int daysToKeep = 30);

private:
    DLLRouter();
    ~DLLRouter();

    // 禁止拷贝和赋值
    DLLRouter(const DLLRouter&) = delete;
    DLLRouter& operator=(const DLLRouter&) = delete;

    // 进度回调
    ProgressCallback progressCallback_;

    // DLL相关
    std::unordered_map<std::string, HMODULE> dllHandles_;
    typedef std::string (*DLLFunction)(const std::string&, ProgressCallback, const std::string&);
    std::unordered_map<std::string, DLLFunction> functions_;
    std::unordered_map<std::string, DllManifest> dllManifests_; // 存储DLL清单信息

    // 插件管理相关方法
    bool loadDllFromManifest(const std::string& manifestPath, const DllManifest& manifest);
    bool parseManifestFile(const std::string& path, DllManifest& manifest);
    bool registerFunctionsFromManifest(const DllManifest& manifest, HMODULE dllHandle);
    bool loadDllInternal(const std::string& name, const std::string& path);

    // 任务队列相关
    static const int MAX_CONCURRENT_TASKS = 5; // 最大并发任务数
    std::priority_queue<Task*, std::vector<Task*>, TaskComparator> taskQueue_;
    std::mutex taskQueueMutex_;
    std::condition_variable taskQueueCv_;
    std::atomic<bool> running_;
    std::atomic<int> activeTaskCount_{0}; // 当前活跃任务数
    std::vector<std::thread> taskThreads_; // 任务线程池

    // 进度队列相关
    std::queue<ProgressUpdate> progressQueue_;
    std::mutex progressMutex_;
    std::condition_variable progressCv_;
    std::thread progressThread_;

    // 任务管理
    std::unordered_map<std::string, std::unique_ptr<Task>> tasks_;

    // 线程池相关方法
    void initThreadPool(); // 初始化线程池
    void taskWorker(); // 工作线程函数
    void executeTask(Task* task); // 执行单个任务

    // 处理进度更新
    void processProgressUpdates();

    // 数据库操作
    void updateTaskStatus(const std::string& taskId, TaskStatus status, const std::string& result = "");
    void saveTaskResult(const std::string& taskId, const std::string& result);

    // 辅助函数

    // 错误处理
    std::string lastError_;
    void setError(const std::string& error) {
        lastError_ = error;
        LogError("DLLRouter error: " + error);
    }
};