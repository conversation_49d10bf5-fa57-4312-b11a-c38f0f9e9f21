@echo off
echo ========================================
echo 修复 RC2175 错误 - ICO 格式问题
echo ========================================
echo.
echo 当前的 jma.ico 文件格式不兼容，需要重新创建。
echo.
echo 解决方案：
echo.
echo 方法1：使用在线转换器（推荐）
echo 1. 访问：https://www.icoconverter.com/
echo 2. 上传 jma.svg 文件
echo 3. 选择尺寸：16x16, 32x32, 48x48, 256x256
echo 4. 下载生成的 ICO 文件
echo 5. 重命名为 jma.ico 并替换当前文件
echo.
echo 方法2：使用 GIMP（免费软件）
echo 1. 下载安装 GIMP：https://www.gimp.org/
echo 2. 打开 jma.svg
echo 3. 图像 ^> 缩放图像 ^> 设置为 256x256
echo 4. 文件 ^> 导出为 ^> 选择 ICO 格式
echo.
echo 方法3：使用 ImageMagick
if exist "C:\Program Files\ImageMagick*\magick.exe" (
    echo ImageMagick 已安装，正在转换...
    "C:\Program Files\ImageMagick*\magick.exe" jma.svg -background transparent -resize 256x256 -define icon:auto-resize=256,128,64,48,32,16 jma_new.ico
    if exist jma_new.ico (
        echo 转换成功！请将 jma_new.ico 重命名为 jma.ico
    )
) else (
    echo ImageMagick 未安装
    echo 下载地址：https://imagemagick.org/script/download.php#windows
)
echo.
echo 完成后，取消注释 fastmb.rc 文件中的图标行，然后重新编译。
echo.
pause
