#include "DLLRouter.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include "APIFunc.h"

// DLLRouter类的线程池实现部分

// 线程池初始化
void DLLRouter::initThreadPool() {
    for (int i = 0; i < MAX_CONCURRENT_TASKS; i++) {
        taskThreads_.emplace_back(&DLLRouter::taskWorker, this);
    }
}

// 工作线程入口函数
void DLLRouter::taskWorker() {
    while (running_) {
        Task* task = nullptr;
        
        // 等待任务
        {
            std::unique_lock<std::mutex> lock(taskQueueMutex_);
            taskQueueCv_.wait(lock, [this] {
                return !taskQueue_.empty() || !running_;
            });
            
            if (!running_) break;
            
            if (!taskQueue_.empty()) {
                task = taskQueue_.top();
                taskQueue_.pop();
            }
        }
        
        if (task) {
            // 增加活跃任务计数
            activeTaskCount_++;
            
            // 执行任务
            executeTask(task);
            
            // 减少活跃任务计数
            activeTaskCount_--;
        }
    }
}

// 执行单个任务
void DLLRouter::executeTask(Task* task) {
    // 更新状态
    {
        std::lock_guard<std::mutex> lock(taskQueueMutex_);
        task->status = TaskStatus::RUNNING;
        task->progress = 0; // 重置进度
    }
    
    // 通知状态变更
    updateTaskStatus(task->taskId, TaskStatus::RUNNING);
    
    try {
        std::string funcKey = task->dllName + "::" + task->funcName;
        DLLFunction functionToCall = nullptr;
        
        // 获取函数指针 (需要加锁)
        {
            std::lock_guard<std::mutex> lock(taskQueueMutex_);
            auto it = functions_.find(funcKey);
            if (it != functions_.end()) {
                functionToCall = it->second;
            }
        }
        
        if (functionToCall) {
            // 执行函数并获取结果
            std::string result = functionToCall(task->params, TaskProgressCallback, task->taskId);
            
            // 更新任务状态
            {
                std::lock_guard<std::mutex> lock(taskQueueMutex_);
                task->result = result;
                task->status = TaskStatus::COMPLETED;
                task->progress = 100;
            }
            
            // 通知状态变更
            updateTaskStatus(task->taskId, TaskStatus::COMPLETED, task->result);
        } else {
            // 函数未找到
            std::string errorMsg = "Function not found: " + funcKey;
            
            // 更新任务状态
            {
                std::lock_guard<std::mutex> lock(taskQueueMutex_);
                task->result = errorMsg;
                task->status = TaskStatus::FAILED;
            }
            
            // 通知状态变更
            updateTaskStatus(task->taskId, TaskStatus::FAILED, errorMsg);
        }
    } catch (const std::exception& e) {
        // 异常处理
        std::string errorMsg = "Exception: " + std::string(e.what());
        
        // 更新任务状态
        {
            std::lock_guard<std::mutex> lock(taskQueueMutex_);
            task->result = errorMsg;
            task->status = TaskStatus::FAILED;
        }
        
        // 通知状态变更
        updateTaskStatus(task->taskId, TaskStatus::FAILED, errorMsg);
    }
} 