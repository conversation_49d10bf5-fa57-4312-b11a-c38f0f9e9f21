#include "DLLRouter.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include <fstream>
#include <direct.h>
#include <io.h>
#include <Windows.h>
#include "APIFunc.h"
#include "../utils/Utils.h"
#include "../utils/MemoryDataManager.h"

// 获取插件目录路径
std::string DLLRouter::GetPluginPath() {
    try {
        // 优先使用UTF-8编码安全的版本
        std::string pluginPath = Utils::getPluginPathUtf8();
        LogInfo("Plugin directory path: " + pluginPath);
        return pluginPath;
    } catch (const std::exception& e) {
        LogError("Exception in GetPluginPath: " + std::string(e.what()));
        // 回退到传统方式
        try {
            return Utils::getPluginPath();
        } catch (...) {
            return "plugin\\";
        }
    } catch (...) {
        LogError("Unknown exception in GetPluginPath");
        return "plugin\\";
    }
}

// 全局进度回调处理函数
void TaskProgressCallback(const std::string& taskId, int progress) {
    // 异步处理进度更新，改为使用队列存储进度更新
    DLLRouter::getInstance().queueProgressUpdate(taskId, progress);
}

DLLRouter::DLLRouter() : running_(true), progressCallback_(nullptr) {
    try {
        LogInfo("Initializing DLLRouter");

        // 启动进度处理线程
        LogInfo("Starting progress processing thread");
        progressThread_ = std::thread(&DLLRouter::processProgressUpdates, this);

        // 初始化任务线程池
        LogInfo("Initializing task thread pool");
        initThreadPool();

        // 自动扫描并加载插件目录中的DLL
        LogInfo("Scanning and loading plugins");
        if (!scanAndLoadPlugins()) {
            LogWarning("Failed to load plugins: " + getLastError());
        }

        LogInfo("DLLRouter initialized successfully");
    } catch (const std::exception& e) {
        LogError("Exception during DLLRouter initialization: " + std::string(e.what()));
    } catch (...) {
        LogError("Unknown exception during DLLRouter initialization");
    }
}

DLLRouter::~DLLRouter() {
    try {
        LogInfo("Shutting down DLLRouter");

        // 停止所有线程
        running_ = false;

        // 通知并等待任务处理线程退出
        LogInfo("Stopping task threads");
        taskQueueCv_.notify_all();
        for (auto& thread : taskThreads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }

        // 通知并等待进度处理线程退出
        LogInfo("Stopping progress thread");
        progressCv_.notify_one();
        if (progressThread_.joinable()) {
            progressThread_.join();
        }

        // 清理资源
        LogInfo("Cleaning up DLL resources");
        for (auto& handle : dllHandles_) {
            FreeLibrary(handle.second);
        }
        dllHandles_.clear();
        functions_.clear();

        LogInfo("DLLRouter shutdown complete");
    } catch (const std::exception& e) {
        LogError("Exception during DLLRouter shutdown: " + std::string(e.what()));
    } catch (...) {
        LogError("Unknown exception during DLLRouter shutdown");
    }
}

void DLLRouter::registerProgressCallback(ProgressCallback callback) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);
    progressCallback_ = callback;
}

// 异步进度更新（新方法）
void DLLRouter::queueProgressUpdate(const std::string& taskId, int progress) {
    // 创建进度更新对象并加入队列
    {
        std::lock_guard<std::mutex> lock(progressMutex_);
        // 将进度限制在0-100范围内
        int validProgress = (progress < 0) ? 0 : (progress > 100 ? 100 : progress);
        progressQueue_.push(ProgressUpdate(taskId, validProgress));
    }
    // 通知进度处理线程
    progressCv_.notify_one();
}

// 进度处理线程函数（新方法）
void DLLRouter::processProgressUpdates() {
    while(running_) {
        ProgressUpdate update("", 0);

        // 等待并获取进度更新
        {
            std::unique_lock<std::mutex> lock(progressMutex_);
            progressCv_.wait(lock, [this]() {
                return !progressQueue_.empty() || !running_;
            });

            if(!running_ && progressQueue_.empty()) break;

            if(!progressQueue_.empty()) {
                update = progressQueue_.front();
                progressQueue_.pop();
            }
        }

        // 实际更新进度（如果有有效的进度更新）
        if(!update.taskId.empty()) {
            updateTaskProgress(update.taskId, update.progress);
        }
    }
}

// 更新任务进度
void DLLRouter::updateTaskProgress(const std::string& taskId, int progress) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    auto it = tasks_.find(taskId);
    if(it != tasks_.end()) {
        // 确保进度在0-100范围内
        int validProgress = progress;
        if (validProgress < 0) validProgress = 0;
        if (validProgress > 100) validProgress = 100;

        // 只有当进度变化时才更新
        if(it->second->progress != validProgress) {
            it->second->progress = validProgress;

            // 保存进度到内存
            TaskData data;
            data.taskId = taskId;
            data.dllName = it->second->dllName;  // 添加DLL名称
            data.funcName = it->second->funcName; // 添加函数名
            data.dataType = "progress";
            data.content = std::to_string(validProgress);
            data.progress = validProgress;
            data.timestamp = Utils::getCurrentTimestamp();
            data.params = it->second->params;  // 添加请求参数
            MemoryDataManager::getInstance().insertTaskData(data);

            // 通知前端进度更新 - 保持现有逻辑不变
            NotifyTaskStatusUpdate(taskId, static_cast<int>(it->second->status), "", validProgress);
        }
    }
}

// 获取任务进度
int DLLRouter::getTaskProgress(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    auto it = tasks_.find(taskId);
    if(it != tasks_.end()) {
        return it->second->progress;
    }
    return 0;
}

// 自动扫描并加载插件目录中的DLL
bool DLLRouter::scanAndLoadPlugins(const std::string& pluginDir) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    try {
        // 获取插件目录的完整路径
        std::string fullPluginDir = GetPluginPath();
        LogInfo("Scanning plugin directory: " + fullPluginDir);

        // 检查目录是否存在（使用UTF-8编码安全的方式）
        std::wstring widePluginDir = Utils::utf8ToWide(fullPluginDir);
        if (_waccess(widePluginDir.c_str(), 0) != 0) {
            LogInfo("Creating plugin directory: " + fullPluginDir);
            _wmkdir(widePluginDir.c_str());

            if (_waccess(widePluginDir.c_str(), 0) != 0) {
                setError("Failed to create plugin directory: " + fullPluginDir);
                return false;
            }
        }

        // 扫描目录中的所有JSON清单文件
        int loadedCount = 0;
        std::set<std::string> loadedFromManifest;

        // 使用Windows API扫描目录（使用UTF-8编码安全的方式）
        WIN32_FIND_DATAW findDataW;
        std::string searchPath = fullPluginDir + "*.json";
        std::wstring wideSearchPath = Utils::utf8ToWide(searchPath);
        LogInfo("Searching for manifest files: " + searchPath);

        HANDLE hFind = FindFirstFileW(wideSearchPath.c_str(), &findDataW);

        if (hFind != INVALID_HANDLE_VALUE) {
            do {
                // 跳过目录
                if ((findDataW.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) != 0) {
                    continue;
                }

                std::string fileName = Utils::wideToUtf8(findDataW.cFileName);
                std::string manifestPath = fullPluginDir + fileName;

                LogInfo("Found manifest file: " + manifestPath);

                // 尝试从清单文件加载DLL
                DllManifest manifest; // 提前声明以获取 manifest.name
                if (parseManifestFile(manifestPath, manifest)) { // 先解析获取name
                    if (loadDllFromManifest(manifestPath, manifest)) { // 传入解析好的 manifest
                        loadedCount++;
                        loadedFromManifest.insert(manifest.name); // 记录已从清单加载的DLL名称
                    } else {
                        LogWarning("Failed to load DLL from manifest: " + manifestPath + ", error: " + getLastError());
                    }
                } else {
                     LogWarning("Failed to parse manifest file: " + manifestPath + ", error: " + getLastError());
                }

            } while (FindNextFileW(hFind, &findDataW));

            FindClose(hFind);
        } else {
            LogInfo("No manifest files found in: " + fullPluginDir);
        }

        // 尝试直接加载目录中的DLL文件 (作为补充，或者在没有manifest时)
        searchPath = fullPluginDir + "*.dll";
        wideSearchPath = Utils::utf8ToWide(searchPath);
        LogInfo("Searching for DLL files directly: " + searchPath);

        hFind = FindFirstFileW(wideSearchPath.c_str(), &findDataW);

        if (hFind != INVALID_HANDLE_VALUE) {
            do {
                // 跳过目录
                if ((findDataW.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) != 0) {
                    continue;
                }

                std::string fileName = Utils::wideToUtf8(findDataW.cFileName);
                std::string dllPath = fullPluginDir + fileName;
                std::string dllName = fileName;
                size_t dotPos = dllName.find_last_of(".");
                if (dotPos != std::string::npos) {
                    dllName = dllName.substr(0, dotPos);
                }

                // 如果这个DLL已经通过manifest成功加载，则跳过直接加载尝试
                if (loadedFromManifest.count(dllName)) {
                    LogInfo("Skipping direct load for DLL already loaded via manifest: " + dllName);
                    continue;
                }

                LogInfo("Found DLL file for potential direct load: " + dllPath);

                // 尝试直接加载DLL
                if (loadDllInternal(dllName, dllPath)) {
                    LogInfo("Successfully loaded DLL directly: " + dllName + " from " + dllPath);
                    // 尝试查找并注册对应的 Manifest 功能
                    std::string potentialManifestPath = fullPluginDir + dllName + ".json";
                    DllManifest manifest;
                    std::wstring widePotentialManifestPath = Utils::utf8ToWide(potentialManifestPath);
                    if (_waccess(widePotentialManifestPath.c_str(), 0) == 0) { // 检查 manifest 是否存在
                        LogInfo("Found corresponding manifest for directly loaded DLL: " + potentialManifestPath);
                        if (parseManifestFile(potentialManifestPath, manifest)) {
                            if (dllHandles_.count(dllName)) {
                                HMODULE handle = dllHandles_[dllName];
                                if (!registerFunctionsFromManifest(manifest, handle)) {
                                    LogWarning("Some functions from manifest could not be registered for directly loaded DLL: " + dllName);
                                }
                                loadedCount++; // 只有成功注册了才算加载成功
                                loadedFromManifest.insert(dllName); // 标记为已处理，避免重复
                            } else {
                                 LogWarning("Handle not found after successful direct load for DLL: " + dllName);
                            }
                        } else {
                            LogWarning("Failed to parse corresponding manifest: " + potentialManifestPath + ", error: " + getLastError());
                        }
                    } else {
                        LogInfo("No corresponding manifest found for directly loaded DLL: " + dllName + ". Functions cannot be registered.");
                        // 如果没有 manifest，我们无法知道要注册哪些函数，所以这个DLL实际上是不可用的
                        // 可以考虑卸载它，或者保留句柄但不增加 loadedCount
                        // 这里选择不增加 loadedCount，只保留句柄
                    }

                } else {
                    // 仅当不是因为"已加载"错误而失败时才记录警告
                    if (getLastError().find("already loaded") == std::string::npos) {
                        LogWarning("Failed to load DLL directly: " + dllName + " from " + dllPath + ", error: " + getLastError());
                    }
                }

            } while (FindNextFileW(hFind, &findDataW));

            FindClose(hFind);
        } else {
            LogInfo("No DLL files found directly in: " + fullPluginDir);
        }

        if (loadedCount > 0) {
            LogInfo("Successfully loaded and registered functions for " + std::to_string(loadedCount) + " plugins from " + fullPluginDir);
            return true;
        } else {
            setError("No valid plugins with registerable functions found in " + fullPluginDir);
            LogWarning("No valid plugins found in " + fullPluginDir); // 改为 Warning
            return false; // 如果最终没有加载并注册任何插件，则返回 false
        }
    } catch (const std::exception& e) {
        setError("Error scanning plugin directory: " + std::string(e.what()));
        return false;
    }
}

// 从清单文件加载DLL (接受已解析的Manifest)
bool DLLRouter::loadDllFromManifest(const std::string& manifestPath, const DllManifest& manifest) {
    try {
        // 清单已在外部解析传入

        // 检查DLL是否已经加载 (使用manifest.name)0
        if (dllHandles_.find(manifest.name) != dllHandles_.end()) {
            // 如果已加载，检查函数是否已注册
            bool functionsRegistered = false;
            for(const auto& funcName : manifest.functions) {
                 if (functions_.count(manifest.name + "::" + funcName)) {
                     functionsRegistered = true;
                     break;
                 }
            }
            if (functionsRegistered) {
                LogInfo("DLL already loaded and functions registered: " + manifest.name);
                return true;
            } else {
                LogWarning("DLL handle exists but functions not registered for: " + manifest.name + ". Attempting registration.");
                // 尝试注册函数
                HMODULE handle = dllHandles_[manifest.name];
                 if (!registerFunctionsFromManifest(manifest, handle)) {
                    LogWarning("Some functions could not be registered for already loaded DLL: " + manifest.name);
                    // 即使注册失败，也认为加载成功，因为句柄存在
                 }
                 return true; // 即使注册部分失败，句柄存在即认为加载成功
            }
        }

        // 构造DLL路径
        std::string manifestDir = manifestPath.substr(0, manifestPath.find_last_of("\\") + 1);
        std::string dllFilePath = manifestDir + manifest.name + ".dll";

        // 检查DLL文件是否存在（使用UTF-8编码安全的方式）
        std::wstring wideDllFilePath = Utils::utf8ToWide(dllFilePath);
        if (_waccess(wideDllFilePath.c_str(), 0) != 0) {
            setError("DLL file not found in manifest directory: " + dllFilePath);
            return false; // 如果在manifest同目录找不到，直接失败
        }

        // 加载DLL
        if (!loadDllInternal(manifest.name, dllFilePath)) {
            // loadDllInternal 已经设置并记录了详细错误信息
            return false;
        }

        // 获取DLL句柄
        HMODULE handle = dllHandles_[manifest.name];
        dllManifests_[manifest.name] = manifest; // 存储清单信息

        // 注册清单中列出的所有函数
        if (!registerFunctionsFromManifest(manifest, handle)) {
            // 如果函数注册失败，仍然保留DLL加载状态，但返回警告
            LogWarning("Some functions could not be registered for DLL: " + manifest.name);
            // 注意：即使注册失败，我们仍然认为 DLL 加载成功了，因为它在内存中。
            // 调用者 (scanAndLoadPlugins) 会根据 loadedCount 判断最终成功与否
        }

        LogInfo("Successfully loaded DLL and attempted function registration from manifest: " + manifest.name);
        return true;
    } catch (const std::exception& e) {
        setError("Error loading DLL from manifest: " + std::string(e.what()));
        return false;
    }
}

// 解析清单文件
bool DLLRouter::parseManifestFile(const std::string& path, DllManifest& manifest) {
    try {
        // 打开清单文件（使用UTF-8编码安全的方式）
        std::wstring widePath = Utils::utf8ToWide(path);
        std::ifstream file(widePath);
        if (!file.is_open()) {
            setError("Failed to open manifest file: " + path);
            return false;
        }

        // 解析JSON
        json j;
        file >> j;

        // 提取必要字段
        if (!j.contains("name") || !j.contains("functions")) {
            setError("Invalid manifest file format: missing required fields");
            return false;
        }

        manifest.name = j["name"].get<std::string>();

        // 可选字段
        manifest.version = j.contains("version") ? j["version"].get<std::string>() : "1.0.0";
        manifest.description = j.contains("description") ? j["description"].get<std::string>() : "";

        // 提取函数列表
        const auto& functions = j["functions"];
        if (!functions.is_array()) {
            setError("Invalid manifest file format: 'functions' must be an array");
            return false;
        }

        for (const auto& func : functions) {
            if (func.contains("name") && func["name"].is_string()) {
                manifest.functions.push_back(func["name"].get<std::string>());
            }
        }

        if (manifest.functions.empty()) {
            setError("No valid functions defined in manifest");
            return false;
        }

        return true;
    } catch (const json::exception& e) {
        setError("JSON parsing error: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        setError("Error parsing manifest file: " + std::string(e.what()));
        return false;
    }
}

// 从清单注册函数
bool DLLRouter::registerFunctionsFromManifest(const DllManifest& manifest, HMODULE dllHandle) {
    bool allSuccess = true;

    LogInfo("Registering functions for DLL: " + manifest.name + ", function count: " + std::to_string(manifest.functions.size()));

    for (const auto& funcName : manifest.functions) {
        LogInfo("Attempting to register function: " + manifest.name + "::" + funcName);

        FARPROC proc = GetProcAddress(dllHandle, funcName.c_str());
        if (!proc) {
            DWORD error = GetLastError();
            LogWarning("Function not found in DLL: " + manifest.name + "::" + funcName + ", error code: " + std::to_string(error));
            allSuccess = false;
            continue;
        }

        // 注册函数
        std::string fullFuncName = manifest.name + "::" + funcName;
        functions_[fullFuncName] = reinterpret_cast<DLLFunction>(proc);
        LogInfo("Successfully registered function: " + fullFuncName);
    }

    // 打印已注册的函数列表
    LogInfo("All registered functions for DLL " + manifest.name + ":");
    for (const auto& pair : functions_) {
        if (pair.first.find(manifest.name + "::") == 0) {
            LogInfo("  - " + pair.first);
        }
    }

    return allSuccess;
}

// 内部加载DLL方法
bool DLLRouter::loadDllInternal(const std::string& name, const std::string& path) {
    // 注意：这个方法已经假设调用者持有taskQueueMutex_锁

    if(dllHandles_.find(name) != dllHandles_.end()) {
        // 允许重复加载同一个DLL路径，但名称必须唯一。这里改为Info日志
        LogInfo("DLL already loaded with this name: " + name + ". Skipping redundant load attempt for path: " + path);
        return true; // 认为已经加载是成功的
    }

    // 使用UTF-8编码安全的方式加载DLL
    std::wstring widePath = Utils::utf8ToWide(path);
    HMODULE handle = LoadLibraryW(widePath.c_str());
    if(!handle) {
        // 增强错误日志，包含 GetLastError()
        DWORD error = GetLastError();
        std::string errorMsg = "Failed to load DLL: " + name + ", Path: " + path + ", Error code: " + std::to_string(error);
        // 可以根据常见的错误码添加更具体的提示
        switch (error) {
            case ERROR_MOD_NOT_FOUND: errorMsg += " (Module not found or dependency missing)"; break;
            case ERROR_PROC_NOT_FOUND: errorMsg += " (Procedure not found - unlikely here)"; break;
            case ERROR_DLL_INIT_FAILED: errorMsg += " (DLL initialization failed)"; break;
            case ERROR_ACCESS_DENIED: errorMsg += " (Access denied)"; break;
        }
        setError(errorMsg);
        LogError(errorMsg); // 也记录到日志
        return false;
    }

    dllHandles_[name] = handle;
    return true;
}

std::string DLLRouter::executeFunction(const std::string& name, const std::string& params) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    LogInfo("Executing function: " + name + " with params: " + params);

    // 打印当前所有注册的函数
    LogDebug("Currently registered functions:");
    for (const auto& pair : functions_) {
        LogDebug("  - " + pair.first);
    }

    auto it = functions_.find(name);
    if(it == functions_.end()) {
        std::string error = "Function not found: " + name;
        setError(error);
        return "";
    }

    try {
        LogInfo("Found function: " + name + ", preparing to execute");

        // 统一处理所有函数，都传递进度回调
        std::string result;
        LogInfo("Executing function with progress callback");
        result = it->second(params, TaskProgressCallback, ""); // 同步调用不需要任务ID

        LogInfo("Function execution completed successfully: " + name);
        return result;
    } catch(const std::exception& e) {
        std::string error = "Function execution failed: " + std::string(e.what());
        setError(error);
        return "";
    } catch(...) {
        std::string error = "Function execution failed with unknown exception";
        setError(error);
        return "";
    }
}

// 提交任务
std::string DLLRouter::submitTask(const std::string& dllName,
                                const std::string& funcName,
                                const std::string& params,
                                int priority) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    // 生成任务ID
    auto now = std::chrono::system_clock::now();
    auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()
    );
    std::stringstream ss;
    ss << std::hex << now_ms.count();
    std::string taskId = ss.str();

    // 创建任务
    auto task = std::make_unique<Task>();
    task->taskId = taskId;
    task->dllName = dllName;
    task->funcName = funcName;
    task->params = params;
    task->priority = priority;
    task->status = TaskStatus::PENDING;

    // 保存任务
    Task* taskPtr = task.get();
    tasks_[taskId] = std::move(task);

    // 将任务加入队列
    taskQueue_.push(taskPtr);

    // 更新数据库
    updateTaskStatus(taskId, TaskStatus::PENDING);

    // 通知工作线程
    taskQueueCv_.notify_one();

    return taskId;
}

bool DLLRouter::cancelTask(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    auto it = tasks_.find(taskId);
    if(it == tasks_.end()) {
        setError("Task not found: " + taskId);
        return false;
    }

    if(it->second->status == TaskStatus::RUNNING) {
        setError("Cannot cancel running task");
        return false;
    }

    it->second->status = TaskStatus::CANCELED;
    updateTaskStatus(taskId, TaskStatus::CANCELED);
    return true;
}

DLLRouter::TaskStatus DLLRouter::getTaskStatus(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    auto it = tasks_.find(taskId);
    if(it == tasks_.end()) {
        setError("Task not found: " + taskId);
        return TaskStatus::FAILED;
    }

    return it->second->status;
}

std::string DLLRouter::getTaskResult(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskQueueMutex_);

    auto it = tasks_.find(taskId);
    if(it == tasks_.end()) {
        setError("Task not found: " + taskId);
        return "";
    }

    return it->second->result;
}

void DLLRouter::updateTaskStatus(const std::string& taskId, TaskStatus status, const std::string& result) {
    auto it = tasks_.find(taskId);
    if (it == tasks_.end()) {
        return;
    }

    TaskData data;
    data.taskId = taskId;
    data.dllName = it->second->dllName;  // 添加DLL名称
    data.funcName = it->second->funcName; // 添加函数名
    data.dataType = "status";
    data.content = std::to_string(static_cast<int>(status));
    data.progress = (status == TaskStatus::COMPLETED) ? 100 : 0;
    data.timestamp = Utils::getCurrentTimestamp();
    data.params = it->second->params;  // 添加请求参数
    MemoryDataManager::getInstance().insertTaskData(data);

    // 如果是完成状态，同时保存结果
    if(status == TaskStatus::COMPLETED && !result.empty()) {
        saveTaskResult(taskId, result);
    }

    // 通知前端 - 保持现有逻辑不变
    NotifyTaskStatusUpdate(taskId, static_cast<int>(status), result, data.progress);
}

void DLLRouter::saveTaskResult(const std::string& taskId, const std::string& result) {
    auto it = tasks_.find(taskId);
    if (it == tasks_.end()) {
        return;
    }

    // 获取当前时间戳
    std::string timestamp = Utils::getCurrentTimestamp();

    // 保存到内存任务数据
    TaskData data;
    data.taskId = taskId;
    data.dllName = it->second->dllName;  // 添加DLL名称
    data.funcName = it->second->funcName; // 添加函数名
    data.dataType = "result";
    data.content = result;
    data.progress = 100;
    data.timestamp = timestamp;
    data.params = it->second->params;  // 添加请求参数
    MemoryDataManager::getInstance().insertTaskData(data);

    // 直接处理JSON结果到内存
    try {
        // 验证JSON格式并存储到内存
        json jsonData = json::parse(result);

        // 直接存储JSON结果到内存
        MemoryDataManager::getInstance().insertJsonResult(
            taskId,
            it->second->dllName,
            it->second->funcName,
            jsonData,
            it->second->params
        );

        LogInfo("JSON result stored in memory for " + it->second->dllName + "::" + it->second->funcName);
    } catch (const json::parse_error& e) {
        LogWarning("Invalid JSON result, storing as text: " + std::string(e.what()));
        // 即使不是有效JSON，也存储原始结果
    } catch (const std::exception& e) {
        LogError("Exception during JSON result storage: " + std::string(e.what()));
    }
}



// 设置日志级别
void DLLRouter::setLogLevel(LogLevel level) {
    if (Logger::getInstance()) {
        Logger::getInstance()->setLevel(level);
        LogInfo("Log level set to " + std::to_string(static_cast<int>(level)));
    }
}

// 清理旧数据
bool DLLRouter::cleanupOldData(int daysToKeep) {
    try {
        LogInfo("Cleaning up old memory data, keeping data for " + std::to_string(daysToKeep) + " days");
        // 将天数转换为分钟（内存数据管理器使用分钟作为单位）
        int minutesToKeep = daysToKeep * 24 * 60;
        bool result = MemoryDataManager::getInstance().cleanupOldData(minutesToKeep);
        if (result) {
            LogInfo("Memory data cleanup completed successfully");
        } else {
            LogError("Memory data cleanup failed: " + MemoryDataManager::getInstance().getLastError());
        }
        return result;
    } catch (const std::exception& e) {
        LogError("Exception during data cleanup: " + std::string(e.what()));
        return false;
    } catch (...) {
        LogError("Unknown exception during data cleanup");
        return false;
    }
}

// 初始化任务线程池
void DLLRouter::initThreadPool() {
    for (int i = 0; i < MAX_CONCURRENT_TASKS; i++) {
        taskThreads_.push_back(std::thread(&DLLRouter::taskWorker, this));
    }
}

// 工作线程函数
void DLLRouter::taskWorker() {
    while (running_) {
        // 获取任务
        Task* task = nullptr;
        {
            std::unique_lock<std::mutex> lock(taskQueueMutex_);
            taskQueueCv_.wait(lock, [this] {
                return !taskQueue_.empty() || !running_;
            });

            if (!running_) break;

            if (!taskQueue_.empty()) {
                task = taskQueue_.top();
                taskQueue_.pop();
                // 更新任务状态为正在运行
                task->status = TaskStatus::RUNNING;
                updateTaskStatus(task->taskId, TaskStatus::RUNNING);
            }
        }

        // 执行任务
        if (task != nullptr) {
            executeTask(task);
        }
    }
}

// 执行单个任务
void DLLRouter::executeTask(Task* task) {
    if (!task) return;

    LogInfo("Executing task: " + task->taskId + ", function: " + task->dllName + "::" + task->funcName);

    try {
        // 查找函数并执行
        std::string funcKey = task->dllName + "::" + task->funcName;
        DLLFunction func = nullptr;

        {
            std::lock_guard<std::mutex> lock(taskQueueMutex_);
            auto it = functions_.find(funcKey);
            if (it != functions_.end()) {
                func = it->second;
                LogInfo("Found function: " + funcKey);
            } else {
                LogWarning("Function not found: " + funcKey);

                // 打印当前所有注册的函数
                LogDebug("Currently registered functions:");
                for (const auto& pair : functions_) {
                    LogDebug("  - " + pair.first);
                }
            }
        }

        if (func) {
            // 调用函数
            LogInfo("Executing function: " + funcKey + " with params: " + task->params);

            // 统一处理所有函数，都传递进度回调和任务ID
            LogInfo("Using progress callback for all functions");
            std::string result = func(task->params, TaskProgressCallback, task->taskId);

            LogInfo("Function execution completed successfully: " + funcKey);

            // 更新任务状态
            std::lock_guard<std::mutex> lock(taskQueueMutex_);
            auto it = tasks_.find(task->taskId);
            if (it != tasks_.end()) {
                it->second->result = result;
                it->second->status = TaskStatus::COMPLETED;
                updateTaskStatus(task->taskId, TaskStatus::COMPLETED, result);
                LogInfo("Task completed: " + task->taskId);
            }
        } else {
            // 函数不存在
            std::string errorMsg = "Function not found: " + funcKey;
            LogError(errorMsg);

            std::lock_guard<std::mutex> lock(taskQueueMutex_);
            auto it = tasks_.find(task->taskId);
            if (it != tasks_.end()) {
                it->second->status = TaskStatus::FAILED;
                updateTaskStatus(task->taskId, TaskStatus::FAILED, errorMsg);
                LogError("Task failed: " + task->taskId + ", reason: " + errorMsg);
            }
        }
    } catch (const std::exception& e) {
        // 处理异常
        std::string errorMsg = "Task failed: " + std::string(e.what());
        LogError(errorMsg);

        std::lock_guard<std::mutex> lock(taskQueueMutex_);
        auto it = tasks_.find(task->taskId);
        if (it != tasks_.end()) {
            it->second->status = TaskStatus::FAILED;
            updateTaskStatus(task->taskId, TaskStatus::FAILED, errorMsg);
            LogError("Task failed: " + task->taskId + ", reason: " + errorMsg);
        }
    } catch (...) {
        // 处理未知异常
        std::string errorMsg = "Task failed with unknown exception";
        LogError(errorMsg);

        std::lock_guard<std::mutex> lock(taskQueueMutex_);
        auto it = tasks_.find(task->taskId);
        if (it != tasks_.end()) {
            it->second->status = TaskStatus::FAILED;
            updateTaskStatus(task->taskId, TaskStatus::FAILED, errorMsg);
            LogError("Task failed: " + task->taskId + ", reason: " + errorMsg);
        }
    }
}