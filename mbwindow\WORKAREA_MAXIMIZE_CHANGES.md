# Miniblink窗口工作区最大化修改说明

## 修改概述

将miniblink窗口的最大化行为从完全覆盖屏幕（包括任务栏）改为工作区最大化模式，即窗口最大化时不覆盖Windows系统任务栏。

## 主要修改内容

### 1. 新增函数

#### `MaximizeToWorkArea(HWND hWnd)`
- **功能**: 将窗口最大化到工作区域，不覆盖任务栏
- **实现**: 使用`MonitorFromWindow`和`GetMonitorInfo`获取工作区域信息
- **兼容性**: 支持多显示器环境和任务栏在不同位置的情况

#### `IsWorkAreaMaximized(HWND hWnd)`
- **功能**: 检查窗口是否处于工作区最大化状态
- **实现**: 通过窗口属性标记和窗口大小比较来判断
- **用途**: 替代`IsZoomed()`函数用于状态判断

### 2. 修改的函数和消息处理

#### `handleLoadingFinish`函数
- **修改前**: 使用`ShowWindow(hWnd, SW_MAXIMIZE)`
- **修改后**: 使用`MaximizeToWorkArea(hWnd)`

#### `onJsQuery`函数 (0x0002消息处理)
- **修改前**: 使用`IsZoomed(hWnd)`判断状态，使用`SW_MAXIMIZE`最大化
- **修改后**: 使用`IsZoomed(hWnd) || IsWorkAreaMaximized(hWnd)`判断，使用`MaximizeToWorkArea(hWnd)`最大化

#### `UpdateWindowRoundCorner`函数
- **修改前**: 仅使用`IsZoomed(hWnd)`判断是否移除圆角
- **修改后**: 使用`IsZoomed(hWnd) || IsWorkAreaMaximized(hWnd)`判断

#### 窗口消息处理
- **WM_ACTIVATE**: 从最小化恢复时使用工作区最大化
- **WM_NCLBUTTONDBLCLK**: 从最小化恢复时使用工作区最大化
- **WM_WINDOWPOSCHANGED**: 更新最大化状态判断逻辑

### 3. 窗口创建修改

#### `createSimpleMb`函数
- **修改前**: 窗口样式包含`WS_MAXIMIZE`
- **修改后**: 移除`WS_MAXIMIZE`，改为在`handleLoadingFinish`中进行工作区最大化

### 4. 资源清理

#### 窗口销毁时清理
- **WM_NCDESTROY**: 清理`WorkAreaMaximized`属性
- **WM_CLOSE**: 清理`WorkAreaMaximized`属性

## 技术特性

### 动画优化
- **问题解决**: 修复了直接跳转到工作区最大化导致的卡顿问题
- **实现方式**: 使用`DeferWindowPos` API实现平滑的窗口动画
- **动画效果**: 窗口从当前位置平滑扩展到工作区域，提供流畅的视觉体验
- **降级处理**: 如果动画API失败，自动降级到直接设置位置的方式

### 多显示器支持
- 使用`MonitorFromWindow(hWnd, MONITOR_DEFAULTTOPRIMARY)`获取窗口所在显示器
- 自动适应不同显示器的工作区域

### 任务栏位置兼容
- 支持任务栏在屏幕底部、顶部、左侧、右侧的情况
- `MONITORINFO.rcWork`自动处理任务栏占用的区域

### 状态管理
- 使用窗口属性`WorkAreaMaximized`标记工作区最大化状态
- 兼容原有的`IsZoomed()`系统最大化状态判断
- 保持现有的78%还原逻辑不变

### 圆角处理
- 工作区最大化时自动移除窗口圆角
- 还原时恢复圆角效果
- 与原有圆角缓存机制兼容

## 行为变化

### 最大化行为
- **修改前**: 窗口完全覆盖屏幕，包括任务栏区域
- **修改后**: 窗口占满工作区域，任务栏保持可见和可访问

### 还原行为
- **保持不变**: 还原到78%屏幕大小并居中显示

### 圆角效果
- **保持不变**: 最大化时移除圆角，还原时恢复圆角

## 兼容性说明

- **Windows XP兼容**: 使用的API在Windows XP中均可用
- **高DPI支持**: 保持原有的高DPI支持
- **多显示器**: 完全支持多显示器环境
- **不同分辨率**: 自动适应不同屏幕分辨率

## 测试建议

1. **基本功能测试**
   - 窗口启动时的工作区最大化
   - 最大化/还原切换功能
   - 从最小化恢复的行为

2. **多显示器测试**
   - 在不同显示器间移动窗口后的最大化行为
   - 主显示器和副显示器的工作区最大化

3. **任务栏位置测试**
   - 任务栏在底部、顶部、左侧、右侧时的表现
   - 自动隐藏任务栏的兼容性

4. **圆角效果测试**
   - 最大化时圆角的移除
   - 还原时圆角的恢复
   - 窗口大小变化时的圆角更新
