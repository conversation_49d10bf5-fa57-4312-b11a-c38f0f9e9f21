﻿#pragma once

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <string>
#include <map>
#include <set>
#include <mutex>
#include <functional>
#include "../APIfunc/DLLRouter.h"

typedef websocketpp::server<websocketpp::config::asio> WebSocketServer;
typedef websocketpp::connection_hdl ConnectionHandle;

class WebSocketServerImpl {
public:
    using message_ptr = WebSocketServer::message_ptr;
    using LocalFunction = std::function<std::string(const std::string&)>;

    WebSocketServerImpl();
    ~WebSocketServerImpl();

    // 启动服务器
    bool Start(int port);

    // 停止服务器
    void Stop();

    // 向指定客户端发送消息
    bool SendTo(const ConnectionHandle& hdl, const std::string& message);

    // 向所有客户端广播消息
    void Broadcast(const std::string& message);

    // 注册本地函数
    void RegisterFunction(const std::string& name, LocalFunction func);

    // 注意: 所有DLL管理方法已移除
    // 现在使用自动DLL发现与加载机制

    // 运行服务器的方法
    void Run() { m_server.run(); }

private:
    // WebSocket事件处理函数
    void OnOpen(ConnectionHandle hdl);
    void OnClose(ConnectionHandle hdl);
    void OnMessage(ConnectionHandle hdl, message_ptr msg);

    WebSocketServer m_server;
    std::set<ConnectionHandle, std::owner_less<ConnectionHandle>> m_connections;
    std::map<std::string, LocalFunction> m_functions;
    bool m_running;

    // 添加互斥锁保护连接集合
    std::mutex m_connectionsMutex;

    friend void WebSocketThread();  // 声明友元函数
};
