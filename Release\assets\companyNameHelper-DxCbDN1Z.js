import{G as o}from"./index-CiDgRWIm.js";import"./react-vendor-C_YFcUsj.js";import"./pdf-vendor-v7Ax4Mr6.js";import"./ui-vendor-r8U87EL9.js";import"./data-vendor-XqdCGI0D.js";class p{static async getCompanyName(t="公司名称"){try{const a=await o.getInstance().getCompanyName();return a&&a!=="公司名称"?a:t}catch(e){return console.warn("获取公司名称失败，使用默认名称:",e),t}}static async getDeviceCategoryRootName(){const t=await this.getCompanyName("全部设备");return console.log("使用公司名称作为设备分类树根节点:",t),t}static async getDepartmentCategoryRootName(){const t=await this.getCompanyName("全部部门");return console.log("使用公司名称作为部门分类树根节点:",t),t}static async getExportTitle(t="设备台账"){const e=await this.getCompanyName();return e&&e!=="公司名称"?e:t}}export{p as CompanyNameHelper,p as default};
