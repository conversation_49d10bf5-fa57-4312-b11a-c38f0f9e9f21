#pragma once

#include <string>
#include <fstream>
#include <iostream>
#include <mutex>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <direct.h> // for _mkdir
#include <io.h> // for _access

// 日志级别
enum LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARNING = 2,
    LOG_ERROR = 3,
    LOG_FATAL = 4
};

class Logger {
private:
    LogLevel level_;
    std::ofstream logFile_;
    std::mutex mutex_;
    std::string logPath_;
    bool consoleOutput_;

    std::string levelToString(LogLevel level) {
        switch (level) {
            case LOG_DEBUG: return "DEBUG";
            case LOG_INFO: return "INFO";
            case LOG_WARNING: return "WARNING";
            case LOG_ERROR: return "ERROR";
            case LOG_FATAL: return "FATAL";
            default: return "UNKNOWN";
        }
    }

    Logger(const std::string& logPath, LogLevel level = LOG_INFO, bool consoleOutput = true)
        : level_(level), logPath_(logPath), consoleOutput_(consoleOutput) {
        // 确保日志目录存在
        std::string dir = logPath.substr(0, logPath.find_last_of("\\/"));
        if (!dir.empty() && _access(dir.c_str(), 0) != 0) {
            _mkdir(dir.c_str());
        }

        // 打开日志文件
        logFile_.open(logPath, std::ios::app);
        if (!logFile_.is_open()) {
            std::cerr << "Failed to open log file: " << logPath << std::endl;
        }

        // 记录启动信息
        log(LOG_INFO, "Logger initialized");
    }

public:
    ~Logger() {
        if (logFile_.is_open()) {
            log(LOG_INFO, "Logger shutdown");
            logFile_.close();
        }
    }

    // 获取单例实例
    static Logger*& getInstance() {
        static Logger* instance = nullptr;
        return instance;
    }

    // 初始化日志系统
    static void initialize(const std::string& logPath, LogLevel level = LOG_INFO, bool consoleOutput = true) {
        if (getInstance() == nullptr) {
            getInstance() = new Logger(logPath, level, consoleOutput);
        }
    }

    // 释放日志系统
    static void shutdown() {
        if (getInstance() != nullptr) {
            delete getInstance();
            getInstance() = nullptr;
        }
    }

    void setLevel(LogLevel level) {
        std::lock_guard<std::mutex> lock(mutex_);
        level_ = level;
    }

    void setConsoleOutput(bool enable) {
        std::lock_guard<std::mutex> lock(mutex_);
        consoleOutput_ = enable;
    }

    void log(LogLevel level, const std::string& message) {
        if (level < level_) return;

        std::lock_guard<std::mutex> lock(mutex_);

        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        std::tm tm_buf;
        localtime_s(&tm_buf, &time);

        // 格式化时间
        std::stringstream ss;
        ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");

        // 构建日志消息
        std::string logMessage = ss.str() + " [" + levelToString(level) + "] " + message;

        // 输出到控制台
        if (consoleOutput_) {
            if (level == LOG_ERROR || level == LOG_FATAL) {
                std::cerr << logMessage << std::endl;
            } else {
                std::cout << logMessage << std::endl;
            }
        }

        // 写入日志文件
        if (logFile_.is_open()) {
            logFile_ << logMessage << std::endl;
            logFile_.flush();
        }
    }

    // 便捷方法
    void debug(const std::string& message) {
        log(LOG_DEBUG, message);
    }

    void info(const std::string& message) {
        log(LOG_INFO, message);
    }

    void warning(const std::string& message) {
        log(LOG_WARNING, message);
    }

    void error(const std::string& message) {
        log(LOG_ERROR, message);
    }

    void fatal(const std::string& message) {
        log(LOG_FATAL, message);
    }
};

// 全局便捷函数
inline void LogDebug(const std::string& message) {
    if (Logger::getInstance()) {
        Logger::getInstance()->debug(message);
    }
}

inline void LogInfo(const std::string& message) {
    if (Logger::getInstance()) {
        Logger::getInstance()->info(message);
    }
}

inline void LogWarning(const std::string& message) {
    if (Logger::getInstance()) {
        Logger::getInstance()->warning(message);
    }
}

inline void LogError(const std::string& message) {
    if (Logger::getInstance()) {
        Logger::getInstance()->error(message);
    }
}

inline void LogFatal(const std::string& message) {
    if (Logger::getInstance()) {
        Logger::getInstance()->fatal(message);
    }
}
