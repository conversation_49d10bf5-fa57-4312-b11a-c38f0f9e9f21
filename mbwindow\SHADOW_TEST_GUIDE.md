# 窗口阴影效果测试指南

## 测试环境要求

### 系统要求
- Windows Vista 或更高版本
- 启用了桌面窗口管理器 (DWM)
- 建议使用 Windows 10/11 以获得最佳效果

### 测试准备
1. 确保桌面背景为纯色或简单图案
2. 关闭其他可能干扰视觉的应用程序
3. 编译并运行程序

## 测试步骤

### 1. 基本阴影效果测试
1. 启动应用程序
2. 观察窗口周围是否出现淡淡的阴影
3. 尝试将窗口移动到不同颜色的背景区域
4. 验证阴影是否让窗口边界更加清晰

**预期结果**：窗口周围应该有一圈淡色阴影，使窗口在任何背景下都清晰可见

### 2. 窗口状态切换测试
1. 点击最大化按钮或使用快捷键
2. 观察最大化后阴影是否消失
3. 点击还原按钮
4. 观察还原后阴影是否重新出现

**预期结果**：
- 最大化时：阴影消失，窗口占满工作区
- 还原时：阴影重新出现，窗口恢复圆角

### 3. 圆角与阴影兼容性测试
1. 观察窗口的圆角效果
2. 确认阴影是否跟随圆角形状
3. 调整窗口大小，观察阴影是否正确更新

**预期结果**：阴影应该与圆角完美配合，不会出现方形阴影

### 4. 多窗口测试
1. 打开多个子窗口（如果支持）
2. 观察每个窗口是否都有阴影效果
3. 测试窗口层叠时的阴影表现

**预期结果**：所有窗口都应该有一致的阴影效果

## 调试信息检查

### 查看调试输出
如果您有调试工具，可以查看以下输出信息：

**成功信息**：
```
Window shadow enabled successfully
```

**警告信息**：
```
DWM not available or not enabled, shadow not applied
```

**错误信息**：
```
Failed to set DWMWA_NCRENDERING_POLICY
Failed to set DWMWA_ALLOW_NCPAINT
Failed to extend frame for shadow
```

### 常见问题排查

#### 问题1：看不到阴影效果
**可能原因**：
- DWM未启用
- 系统版本过低
- 显卡驱动问题

**解决方案**：
1. 检查系统版本（需要Vista+）
2. 启用桌面合成效果
3. 更新显卡驱动

#### 问题2：阴影显示异常
**可能原因**：
- 系统主题设置
- 高DPI设置冲突

**解决方案**：
1. 尝试切换到Windows默认主题
2. 检查DPI设置

#### 问题3：性能问题
**可能原因**：
- 硬件加速未启用
- 显卡性能不足

**解决方案**：
1. 启用硬件加速
2. 降低视觉效果设置

## 视觉对比测试

### 测试场景
1. **白色背景**：将窗口拖到白色区域，观察阴影对比度
2. **深色背景**：将窗口拖到深色区域，观察阴影可见性
3. **复杂背景**：在有图案的背景上测试窗口识别度

### 评估标准
- ✅ 阴影清晰可见
- ✅ 窗口边界明确
- ✅ 视觉层次感良好
- ✅ 不影响内容显示

## 报告问题

如果发现问题，请记录：
1. 操作系统版本
2. 显卡型号和驱动版本
3. 具体的问题现象
4. 调试输出信息
5. 重现步骤

这些信息将帮助快速定位和解决问题。
