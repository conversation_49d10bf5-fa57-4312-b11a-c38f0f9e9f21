# 窗口阴影效果增强

## 概述

为了解决无边框窗口在相同颜色背景下难以区分的问题，我们为项目添加了系统级的窗口阴影效果。这个功能使用Windows DWM (Desktop Window Manager) API实现，提供了原生的、硬件加速的阴影效果。

## 实现的功能

### 1. 自动阴影管理
- **正常状态**：窗口显示美观的阴影效果，增强视觉层次感
- **最大化状态**：自动禁用阴影以获得更好的性能和视觉效果
- **状态切换**：在窗口最大化和还原时自动切换阴影状态

### 2. 兼容性检查
- 自动检测Windows版本（需要Vista及以上）
- 检查DWM是否启用
- 在不支持的系统上优雅降级

### 3. 性能优化
- 使用硬件加速的DWM阴影
- 避免频繁的阴影设置操作
- 与现有的圆角功能完美集成

## 技术实现

### 新增的API依赖
```cpp
#include <dwmapi.h>
#pragma comment(lib, "dwmapi.lib")
```

### 核心函数
- `SetWindowShadow(HWND hWnd, bool enable)` - 设置窗口阴影效果
- 集成到 `UpdateWindowRoundCorner()` 函数中

### 使用的Windows API
- `DwmIsCompositionEnabled()` - 检查DWM是否启用
- `DwmSetWindowAttribute()` - 设置窗口属性
- `DwmExtendFrameIntoClientArea()` - 扩展窗口框架以支持阴影

## 视觉效果

### 改进前
- 无边框窗口在相同颜色背景下难以区分
- 缺乏视觉层次感

### 改进后
- 清晰的阴影边界，易于识别窗口边缘
- 增强的视觉层次感和现代化外观
- 与系统主题保持一致的阴影效果

## 兼容性

### 支持的系统
- Windows Vista 及以上版本
- 需要启用DWM（桌面窗口管理器）

### 降级处理
- 在不支持的系统上自动跳过阴影设置
- 不影响程序的正常运行
- 输出调试信息便于问题诊断

## 使用说明

阴影效果会在以下情况下自动应用：
1. 主窗口创建时
2. 子窗口创建时
3. 窗口状态变化时（最大化/还原）

无需手动调用，系统会自动管理阴影状态。

## 调试信息

程序会输出以下调试信息：
- `"Window shadow enabled successfully"` - 阴影启用成功
- `"Window shadow disabled"` - 阴影已禁用
- `"DWM not available or not enabled"` - DWM不可用
- 各种API调用失败的错误信息

## 注意事项

1. 阴影效果依赖于Windows DWM，在禁用DWM的系统上不会显示
2. 最大化窗口时会自动禁用阴影以提高性能
3. 阴影效果与现有的圆角功能完全兼容
4. 不会影响窗口的交互功能和性能
