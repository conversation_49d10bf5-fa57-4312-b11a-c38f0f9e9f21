# 单实例检查功能更新

## 问题描述
用户反馈：当台账系统已经打开时，再次尝试打开项目会显示"数据库初始化失败"的错误信息，并且会弹出第二个项目实例。

## 解决方案
实现了单实例检查机制，确保同一时间只能运行一个台账系统实例。

## 主要修改

### 1. 添加全局变量
```cpp
HANDLE g_singleInstanceMutex = NULL;  // 单实例互斥锁
```

### 2. 新增函数
- `bool CheckSingleInstance()` - 检查是否为唯一实例
- `void ActivateExistingWindow()` - 激活已存在的窗口
- `void ReleaseSingleInstanceMutex()` - 释放互斥锁资源

### 3. 单实例检查逻辑
- 使用命名互斥锁 `TaiZhangSystem_SingleInstance_Mutex`
- 在程序启动时首先检查是否已有实例运行
- 如果发现已有实例，则：
  - 尝试激活已存在的窗口
  - 显示友好提示："台账系统已经打开，请查看任务栏或桌面上的窗口。"
  - 退出当前启动尝试

### 4. 窗口激活功能
- 使用 `FindWindowW()` 查找已存在的窗口
- 如果窗口被最小化，先恢复窗口
- 将窗口带到前台并确保可见

### 5. 资源清理
- 在程序正常退出时释放互斥锁
- 在异常情况下也确保释放互斥锁

## 用户体验改进
1. **友好提示**：不再显示"数据库初始化失败"，而是显示清晰的中文提示
2. **自动激活**：自动将已运行的窗口带到前台，用户无需手动查找
3. **防止重复实例**：确保系统资源不被浪费，避免数据冲突

## 技术实现细节
- 使用Windows API `CreateMutexW()` 创建命名互斥锁
- 通过 `GetLastError()` 检查 `ERROR_ALREADY_EXISTS` 状态
- 使用 `FindWindowW()` 查找窗口类名为 `kClassWindow` 的窗口
- 使用 `SetForegroundWindow()` 和 `BringWindowToTop()` 激活窗口

## 兼容性
- 兼容 Windows XP 及以上版本
- 使用标准 Windows API，无需额外依赖
- 符合 C++14 标准

## 测试建议
1. 启动台账系统
2. 再次尝试启动台账系统
3. 验证是否显示正确的提示信息
4. 验证已存在的窗口是否被正确激活
5. 验证程序退出后是否可以正常重新启动
