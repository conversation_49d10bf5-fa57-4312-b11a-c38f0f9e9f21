#pragma once

#include <string>
#include <queue>
#include <mutex>
#include <thread>
#include <condition_variable>
#include <atomic>
#include <unordered_map>
#include <unordered_set>
#include <nlohmann/json.hpp>
#include "../utils/Logger.h"
#include "DatabaseManager.h"

using json = nlohmann::json;

// JSON处理任务结构
struct JsonProcessTask {
    std::string taskId;
    std::string dllName;
    std::string funcName;
    std::string jsonContent;
    std::string params;
    std::string timestamp;

    JsonProcessTask() = default;
    JsonProcessTask(const std::string& id, const std::string& dll, const std::string& func,
                   const std::string& content, const std::string& p, const std::string& ts)
        : taskId(id), dllName(dll), funcName(func), jsonContent(content), params(p), timestamp(ts) {}
};

// JSON Schema处理器类
class JsonSchemaProcessor {
public:
    static JsonSchemaProcessor& getInstance() {
        static JsonSchemaProcessor instance;
        return instance;
    }

    // 初始化处理器
    bool initialize();

    // 清理资源
    void cleanup();

    // 提交JSON处理任务
    void submitTask(const JsonProcessTask& task);

    // 获取最后一次错误
    std::string getLastError() const { return lastError_; }

private:
    JsonSchemaProcessor();
    ~JsonSchemaProcessor();
    JsonSchemaProcessor(const JsonSchemaProcessor&) = delete;
    JsonSchemaProcessor& operator=(const JsonSchemaProcessor&) = delete;

    // 处理线程函数
    void processingThread();

    // 处理单个任务
    void processTask(const JsonProcessTask& task);

    // 解析JSON并创建表
    bool createTableFromJson(const std::string& dllName, const std::string& funcName,
                            const std::string& jsonContent);

    // 将JSON数据插入到表中
    bool insertDataIntoTable(const std::string& dllName, const std::string& funcName,
                            const std::string& jsonContent, const std::string& taskId,
                            const std::string& timestamp);

    // 从JSON对象创建表
    bool createTableFromJsonObject(const std::string& tableName, const json& jsonObj);

    // 从JSON数组创建表
    bool createTableFromJsonArray(const std::string& tableName, const json& jsonArray);

    // 确定JSON字段类型
    std::string determineColumnType(const json& value);

    // 生成表名
    std::string generateTableName(const std::string& dllName, const std::string& funcName);

    // 处理列名，避免使用SQLite关键字
    std::string sanitizeColumnName(const std::string& columnName, bool* wasModified = nullptr);

    // 检查表是否存在
    bool tableExists(const std::string& tableName);

    // 获取表的列信息
    std::unordered_map<std::string, std::string> getTableColumns(const std::string& tableName);

    // 更新表结构以匹配新的JSON结构
    bool updateTableStructure(const std::string& tableName,
                             const std::unordered_map<std::string, std::string>& existingColumns,
                             const std::unordered_map<std::string, std::string>& newColumns);

    // 任务队列
    std::queue<JsonProcessTask> taskQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCv_;

    // 处理线程
    std::thread processingThread_;
    std::atomic<bool> running_;

    // 已创建的表缓存
    std::unordered_map<std::string, std::unordered_map<std::string, std::string>> tableSchemaCache_;
    std::mutex schemaCacheMutex_;

    // 字段名映射缓存 (表名 -> (处理后列名 -> 原始列名))
    std::unordered_map<std::string, std::unordered_map<std::string, std::string>> columnMappingCache_;
    std::mutex mappingCacheMutex_;

    // 错误处理
    std::string lastError_;
    void setError(const std::string& error) {
        lastError_ = error;
        LogError("JsonSchemaProcessor error: " + error);
    }
};
