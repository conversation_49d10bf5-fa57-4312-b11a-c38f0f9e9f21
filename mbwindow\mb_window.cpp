#include <winsock2.h>
#define _CRT_SECURE_NO_WARNINGS 1

#include "mb_window.h"
#include <string>
#include <vector>
#include <time.h>
#include <shlwapi.h>
#include <WindowsX.h>
#include <dwmapi.h>

// Link required libraries
#pragma comment(lib, "Shlwapi.lib")
#pragma comment(lib, "dwmapi.lib")

// Define constants
#define ROUND_CORNER_RADIUS 15
#define MIN_WINDOW_WIDTH 200   // Minimum window width (adjust as needed)
#define MIN_WINDOW_HEIGHT 200  // Minimum window height (adjust as needed)

namespace {  // Anonymous namespace for internal linkage

    //=============================================================================
    // Forward Declarations
    //=============================================================================

    // 工作区最大化相关函数声明
    void MaximizeToWorkArea(HWND hWnd);
    bool IsWorkAreaMaximized(HWND hWnd);

    // 窗口阴影相关函数声明
    void SetWindowShadow(HWND hWnd, bool enable = true);

    //=============================================================================
    // Double Buffering
    //=============================================================================

    struct BufferInfo {
        HDC hdcMem;        // Memory DC for buffering
        HBITMAP hbmMem;    // Bitmap for buffering
        HBITMAP hbmOld;    // Original bitmap
        int width;
        int height;
        RECT updateRect;   // Region to update
        bool needsFullRedraw; // Flag for full redraw
        HRGN hUpdateRgn;   // Accumulated update region
        UINT_PTR timerId;  // Timer ID for repaint
    };

    // 创建双缓冲
    bool CreateBuffer(HWND hWnd, BufferInfo* buffer, int width, int height) {
        if (!hWnd || !buffer) return false;

        HDC hdc = GetDC(hWnd);
        if (!hdc) return false;

        buffer->hdcMem = CreateCompatibleDC(hdc);
        if (!buffer->hdcMem) {
            ReleaseDC(hWnd, hdc);
            return false;
        }

        buffer->hbmMem = CreateCompatibleBitmap(hdc, width, height);
        if (!buffer->hbmMem) {
            DeleteDC(buffer->hdcMem);
            ReleaseDC(hWnd, hdc);
            return false;
        }

        buffer->hbmOld = (HBITMAP)SelectObject(buffer->hdcMem, buffer->hbmMem);
        buffer->width = width;
        buffer->height = height;
        buffer->needsFullRedraw = true;
        buffer->updateRect = { 0, 0, width, height };
        buffer->hUpdateRgn = NULL;
        buffer->timerId = 0;

        SetStretchBltMode(buffer->hdcMem, HALFTONE);
        SetBrushOrgEx(buffer->hdcMem, 0, 0, NULL);

        RECT rc = { 0, 0, width, height };
        HBRUSH hBrush = CreateSolidBrush(RGB(255, 255, 255));
        FillRect(buffer->hdcMem, &rc, hBrush);
        DeleteObject(hBrush);

        ReleaseDC(hWnd, hdc);
        return true;
    }

    // 清理双缓冲资源
    void DeleteBuffer(BufferInfo* buffer) {
        if (!buffer) return;
        if (buffer->hbmOld) SelectObject(buffer->hdcMem, buffer->hbmOld);
        if (buffer->hbmMem) DeleteObject(buffer->hbmMem);
        if (buffer->hdcMem) DeleteDC(buffer->hdcMem);
        buffer->hbmMem = NULL;
        buffer->hbmOld = NULL;
        buffer->hdcMem = NULL;
    }

    //=============================================================================
    // Miniblink Callbacks
    //=============================================================================

    void MB_CALL_TYPE handlePaintUpdatedCallback(mbWebView webView, void* param, const HDC hdc, int x, int y, int cx, int cy) {
        HWND hWnd = (HWND)param;
        if (!hWnd || !hdc) return;

        LONG_PTR exStyle = GetWindowLongPtr(hWnd, GWL_EXSTYLE);
        if (exStyle & WS_EX_LAYERED) {
            RECT rectDest;
            GetWindowRect(hWnd, &rectDest);
            SIZE sizeDest = { rectDest.right - rectDest.left, rectDest.bottom - rectDest.top };
            POINT pointSource = { 0, 0 };

            BITMAP bmp = { 0 };
            HBITMAP hBmp = (HBITMAP)GetCurrentObject(hdc, OBJ_BITMAP);
            GetObject(hBmp, sizeof(BITMAP), &bmp);
            sizeDest.cx = bmp.bmWidth;
            sizeDest.cy = bmp.bmHeight;

            HDC hdcScreen = GetDC(hWnd);
            if (hdcScreen) {
                BLENDFUNCTION blend = { AC_SRC_OVER, 0, 255, AC_SRC_ALPHA };
                BOOL success = UpdateLayeredWindow(hWnd, hdcScreen, nullptr, &sizeDest, hdc, &pointSource, RGB(255, 255, 255), &blend, ULW_ALPHA);

                if (!success) {
                    HDC hdcMemory = CreateCompatibleDC(hdcScreen);
                    HBITMAP hbmpMemory = CreateCompatibleBitmap(hdcScreen, sizeDest.cx, sizeDest.cy);
                    if (hdcMemory && hbmpMemory) {
                        HBITMAP hbmpOld = (HBITMAP)SelectObject(hdcMemory, hbmpMemory);
                        BitBlt(hdcMemory, 0, 0, sizeDest.cx, sizeDest.cy, hdc, 0, 0, SRCCOPY | CAPTUREBLT);
                        UpdateLayeredWindow(hWnd, hdcScreen, nullptr, &sizeDest, hdcMemory, &pointSource, RGB(255, 255, 255), &blend, ULW_ALPHA);
                        SelectObject(hdcMemory, hbmpOld);
                        DeleteObject(hbmpMemory);
                        DeleteDC(hdcMemory);
                    }
                }
                ReleaseDC(hWnd, hdcScreen);
            }
        }
        else {
            BufferInfo* buffer = (BufferInfo*)GetProp(hWnd, L"buffer");
            if (buffer) {
                RECT rect = { x, y, x + cx, y + cy };
                HRGN hRgn = CreateRectRgnIndirect(&rect);
                if (buffer->hUpdateRgn == NULL) {
                    buffer->hUpdateRgn = hRgn;
                }
                else {
                    CombineRgn(buffer->hUpdateRgn, buffer->hUpdateRgn, hRgn, RGN_OR);
                    DeleteObject(hRgn);
                }
                if (buffer->timerId == 0) {
                    buffer->timerId = SetTimer(hWnd, 1, 16, NULL); // 16ms ≈ 60 FPS
                }
            }
        }
    }

    void MB_CALL_TYPE onRunJs(mbWebView webView, void* param, mbJsExecState es, mbJsValue v) {
        const utf8* str = mbJsToString(es, v);
        if (str) {
            OutputDebugStringA("onRunJs: ");
            OutputDebugStringA(str);
            OutputDebugStringA("\n");
        }
    }

    void MB_CALL_TYPE handleDocumentReady(mbWebView webView, void* param, mbWebFrameHandle frameId) {
        OutputDebugStringA("HandleDocumentReady\n");
        // mbShowWindow(webView, TRUE);  // 注释掉以避免重置窗口状态

        // 注入禁用文本选择的 CSS
        const char* disableSelectionJS =
            "var style = document.createElement('style');"
            "style.textContent = '*{"
            "-webkit-user-select: none;"
            "-moz-user-select: none;"
            "-ms-user-select: none;"
            "user-select: none;"
            "}';"
            "document.head.appendChild(style);";

        mbRunJs(webView, mbWebFrameGetMainFrame(webView), disableSelectionJS, TRUE, nullptr, nullptr, nullptr);
        mbRunJs(webView, mbWebFrameGetMainFrame(webView), "return window.onNativeRunjs('I am runjs');", TRUE, onRunJs, nullptr, nullptr);
    }

    void MB_CALL_TYPE handleLoadingFinish(mbWebView webView, void* param, mbWebFrameHandle frameId, const utf8* url, mbLoadingResult result, const utf8* failedReason) {
        if (result == MB_LOADING_SUCCEEDED) {
            HWND hWnd = mbGetHostHWND(webView);
            if (hWnd) {
                // 优化的工作区最大化显示 - 减少闪烁
                ShowWindow(hWnd, SW_HIDE);  // 先隐藏
                MaximizeToWorkArea(hWnd);   // 工作区最大化（不覆盖任务栏）
                ShowWindow(hWnd, SW_SHOW);  // 最后显示
                UpdateWindow(hWnd);
            }
        }
        OutputDebugStringA("handleLoadingFinish\n");
    }

    mbWebView MB_CALL_TYPE handleCreateView(mbWebView webView, void* param, mbNavigationType navigationType, const utf8* url, const mbWindowFeatures* windowFeatures) {
        mbWebView view = mbCreateWebView();
        if (!view) return 0;

        HWND hWnd = CreateWindowEx(
            WS_EX_APPWINDOW,
            kClassWindow,
            L"",  // 添加一个空标题，这对任务栏功能很重要
            WS_POPUP | WS_CLIPCHILDREN | WS_CLIPSIBLINGS | WS_SYSMENU | WS_MINIMIZEBOX | WS_THICKFRAME,  // 添加拉伸支持
            windowFeatures->x, windowFeatures->y, windowFeatures->width, windowFeatures->height,
            NULL, NULL, GetModuleHandle(NULL), NULL);
        if (!hWnd) {
            mbDestroyWebView(view);
            return 0;
        }

        LONG_PTR exStyle = GetWindowLongPtr(hWnd, GWL_EXSTYLE);
        SetWindowLongPtr(hWnd, GWL_EXSTYLE, exStyle | WS_EX_COMPOSITED);

        SetProp(hWnd, L"mb", (HANDLE)view);
        SetProp(hWnd, L"subView", (HANDLE)TRUE);
        mbSetHandle(view, hWnd);

        // 为子窗口也设置阴影效果
        SetWindowShadow(hWnd, true);
        mbOnPaintUpdated(view, handlePaintUpdatedCallback, hWnd);
        mbOnLoadingFinish(view, handleLoadingFinish, (void*)view);
        mbOnCreateView(view, handleCreateView, (void*)view);
        mbSetNavigationToNewWindowEnable(view, true);
        mbSetCspCheckEnable(view, false);

        RECT rc;
        GetClientRect(hWnd, &rc);
        mbResize(view, rc.right - rc.left, rc.bottom - rc.top);

        return view;
    }

    void MB_CALL_TYPE onJsQuery(mbWebView webView, void* param, mbJsExecState es, int64_t queryId, int customMsg, const utf8* request) {
        OutputDebugStringA("onJsQuery: ");
        if (request) OutputDebugStringA(request);
        OutputDebugStringA("\n");

        HWND hWnd = mbGetHostHWND(webView);
        if (!hWnd) {
            mbResponseQuery(webView, queryId, customMsg, "Failed to get window handle");
            return;
        }

        switch (customMsg) {
        case 0x0001: // Minimize
            ShowWindow(hWnd, SW_MINIMIZE);
            mbResponseQuery(webView, queryId, customMsg, "Window minimized");
            break;
        case 0x0002: // Maximize/Restore
        {
            if (IsZoomed(hWnd) || IsWorkAreaMaximized(hWnd)) {
                // 当前是最大化状态，还原到78%屏幕大小（与创建时一致）
                int screenWidth = GetSystemMetrics(SM_CXSCREEN);
                int screenHeight = GetSystemMetrics(SM_CYSCREEN);

                // 计算78%屏幕大小
                int windowWidth = static_cast<int>(screenWidth * 0.78);
                int windowHeight = static_cast<int>(screenHeight * 0.78);

                // 计算居中位置
                int x = (screenWidth - windowWidth) / 2;
                int y = (screenHeight - windowHeight) / 2;

                // 优化的还原序列：先还原，然后设置位置和大小
                ShowWindow(hWnd, SW_RESTORE);
                SetWindowPos(hWnd, NULL, x, y, windowWidth, windowHeight,
                           SWP_NOZORDER | SWP_NOACTIVATE);

                // 清除工作区最大化标记
                RemoveProp(hWnd, L"WorkAreaMaximized");

                OutputDebugStringA("Window restored to 78% screen size\n");
                mbResponseQuery(webView, queryId, customMsg, "Window restored to 78% size");
            } else {
                // 当前不是最大化状态，工作区最大化窗口
                MaximizeToWorkArea(hWnd);
                OutputDebugStringA("Window maximized to work area\n");
                mbResponseQuery(webView, queryId, customMsg, "Window maximized to work area");
            }
            break;
        }
        case 0x0003: // Close
            PostMessage(hWnd, WM_CLOSE, 0, 0);
            mbResponseQuery(webView, queryId, customMsg, "Window closing");
            ExitProcess(0); // Consider safer cleanup
            break;
        default:
            mbResponseQuery(webView, queryId, customMsg, "Unknown command");
            break;
        }
    }

    //=============================================================================
    // Window Functions
    //=============================================================================

    // 工作区最大化函数
    void MaximizeToWorkArea(HWND hWnd) {
        if (!hWnd) return;

        // 获取窗口所在的显示器
        HMONITOR hMonitor = MonitorFromWindow(hWnd, MONITOR_DEFAULTTOPRIMARY);
        MONITORINFO mi = { sizeof(MONITORINFO) };

        if (GetMonitorInfo(hMonitor, &mi)) {
            // 使用工作区域（不包括任务栏）
            RECT workArea = mi.rcWork;
            SetWindowPos(hWnd, NULL,
                        workArea.left, workArea.top,
                        workArea.right - workArea.left,
                        workArea.bottom - workArea.top,
                        SWP_NOZORDER | SWP_NOACTIVATE);

            // 设置工作区最大化标记
            SetProp(hWnd, L"WorkAreaMaximized", (HANDLE)TRUE);
            OutputDebugStringA("Window maximized to work area\n");
        }
    }

    // 检查是否处于工作区最大化状态
    bool IsWorkAreaMaximized(HWND hWnd) {
        if (!hWnd) return false;

        // 检查标记
        if (GetProp(hWnd, L"WorkAreaMaximized")) {
            return true;
        }

        // 也检查窗口大小是否匹配工作区
        HMONITOR hMonitor = MonitorFromWindow(hWnd, MONITOR_DEFAULTTOPRIMARY);
        MONITORINFO mi = { sizeof(MONITORINFO) };

        if (GetMonitorInfo(hMonitor, &mi)) {
            RECT windowRect;
            GetWindowRect(hWnd, &windowRect);

            RECT workArea = mi.rcWork;
            return (windowRect.left == workArea.left &&
                    windowRect.top == workArea.top &&
                    windowRect.right == workArea.right &&
                    windowRect.bottom == workArea.bottom);
        }

        return false;
    }

    // 设置窗口阴影效果
    void SetWindowShadow(HWND hWnd, bool enable) {
        if (!hWnd) return;

        // 检查是否支持DWM（Vista及以上版本）
        BOOL dwmEnabled = FALSE;
        HRESULT hr = DwmIsCompositionEnabled(&dwmEnabled);
        if (FAILED(hr) || !dwmEnabled) {
            OutputDebugStringA("DWM not available or not enabled, shadow not applied\n");
            return;
        }

        if (enable) {
            // 启用非客户区渲染
            DWMNCRENDERINGPOLICY policy = DWMNCRP_ENABLED;
            hr = DwmSetWindowAttribute(hWnd, DWMWA_NCRENDERING_POLICY, &policy, sizeof(policy));
            if (FAILED(hr)) {
                OutputDebugStringA("Failed to set DWMWA_NCRENDERING_POLICY\n");
            }

            // 允许非客户区绘制
            BOOL allowNCPaint = TRUE;
            hr = DwmSetWindowAttribute(hWnd, DWMWA_ALLOW_NCPAINT, &allowNCPaint, sizeof(allowNCPaint));
            if (FAILED(hr)) {
                OutputDebugStringA("Failed to set DWMWA_ALLOW_NCPAINT\n");
            }

            // 设置窗口边框和阴影
            MARGINS margins = { 1, 1, 1, 1 };
            hr = DwmExtendFrameIntoClientArea(hWnd, &margins);
            if (SUCCEEDED(hr)) {
                OutputDebugStringA("Window shadow enabled successfully\n");
            } else {
                OutputDebugStringA("Failed to extend frame for shadow\n");
            }
        } else {
            // 禁用阴影（主要用于最大化状态）
            DWMNCRENDERINGPOLICY policy = DWMNCRP_DISABLED;
            DwmSetWindowAttribute(hWnd, DWMWA_NCRENDERING_POLICY, &policy, sizeof(policy));

            MARGINS margins = { 0, 0, 0, 0 };
            DwmExtendFrameIntoClientArea(hWnd, &margins);
            OutputDebugStringA("Window shadow disabled\n");
        }
    }

    // 圆角缓存结构
    struct RoundCornerCache {
        int lastWidth;
        int lastHeight;
        bool isMaximized;
        HRGN hCachedRgn;

        RoundCornerCache() : lastWidth(0), lastHeight(0), isMaximized(false), hCachedRgn(NULL) {}

        ~RoundCornerCache() {
            if (hCachedRgn) {
                DeleteObject(hCachedRgn);
                hCachedRgn = NULL;
            }
        }
    };

    // 获取静态缓存实例
    RoundCornerCache& GetRoundCornerCache() {
        static RoundCornerCache cache;
        return cache;
    }

    void UpdateWindowRoundCorner(HWND hWnd) {
        if (!hWnd) return;

        RoundCornerCache& cache = GetRoundCornerCache();
        bool isMaximized = IsZoomed(hWnd) != 0 || IsWorkAreaMaximized(hWnd);

        if (isMaximized) {
            if (cache.hCachedRgn) {
                DeleteObject(cache.hCachedRgn);
                cache.hCachedRgn = NULL;
            }
            SetWindowRgn(hWnd, NULL, FALSE);  // 使用FALSE减少重绘
            cache.isMaximized = true;

            // 最大化时禁用阴影以获得更好的性能和视觉效果
            SetWindowShadow(hWnd, false);
            return;
        }

        RECT rcWindow;
        GetWindowRect(hWnd, &rcWindow);
        int width = rcWindow.right - rcWindow.left;
        int height = rcWindow.bottom - rcWindow.top;

        // 检查是否需要重新创建圆角区域
        if (cache.lastWidth == width && cache.lastHeight == height &&
            !cache.isMaximized && cache.hCachedRgn) {
            return; // 使用缓存的区域
        }

        // 清理旧的缓存区域
        if (cache.hCachedRgn) {
            DeleteObject(cache.hCachedRgn);
            cache.hCachedRgn = NULL;
        }

        // 创建新的圆角区域
        cache.hCachedRgn = CreateRoundRectRgn(0, 0, width + 1, height + 1, ROUND_CORNER_RADIUS, ROUND_CORNER_RADIUS);
        if (cache.hCachedRgn) {
            SetWindowRgn(hWnd, cache.hCachedRgn, FALSE);  // 使用FALSE减少重绘
            cache.lastWidth = width;
            cache.lastHeight = height;
            cache.isMaximized = false;

            // 非最大化状态启用阴影效果
            SetWindowShadow(hWnd, true);
        }
    }

    LRESULT CALLBACK testWindowProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
        mbWebView view = (mbWebView)GetProp(hWnd, L"mb");
        BufferInfo* buffer = (BufferInfo*)GetProp(hWnd, L"buffer");
        LRESULT result = 0;

        if (!view && msg != WM_CREATE) return DefWindowProc(hWnd, msg, wParam, lParam);

        switch (msg) {
        case WM_CREATE:
            buffer = new(std::nothrow) BufferInfo();
            if (!buffer) return -1;
            memset(buffer, 0, sizeof(BufferInfo));
            SetProp(hWnd, L"buffer", (HANDLE)buffer);
            UpdateWindowRoundCorner(hWnd);
            return 0;

        case WM_NCDESTROY:
            if (GetProp(hWnd, L"subView")) RemoveProp(hWnd, L"subView");
            if (GetProp(hWnd, L"WorkAreaMaximized")) RemoveProp(hWnd, L"WorkAreaMaximized");
            if (buffer) {
                if (buffer->hUpdateRgn) DeleteObject(buffer->hUpdateRgn);
                if (buffer->timerId) KillTimer(hWnd, buffer->timerId);
                DeleteBuffer(buffer);
                delete buffer;
                RemoveProp(hWnd, L"buffer");
            }
            if (view) {
                mbDestroyWebView(view);
                RemoveProp(hWnd, L"mb");
            }
            return 0;

        case WM_NCCALCSIZE:
            if (wParam) return 0; // Remove default frame
            break;

        case WM_ERASEBKGND:
            return TRUE; // Prevent background erase

        case WM_PAINT:
            if (!(GetWindowLong(hWnd, GWL_EXSTYLE) & WS_EX_LAYERED)) {
                PAINTSTRUCT ps;
                HDC hdc = BeginPaint(hWnd, &ps);
                if (!hdc) break;

                RECT rcClient;
                GetClientRect(hWnd, &rcClient);
                int width = rcClient.right - rcClient.left;
                int height = rcClient.bottom - rcClient.top;

                if (!buffer || buffer->width != width || buffer->height != height) {
                    if (buffer) DeleteBuffer(buffer);
                    buffer = new(std::nothrow) BufferInfo();
                    if (!buffer) {
                        EndPaint(hWnd, &ps);
                        break;
                    }
                    SetProp(hWnd, L"buffer", (HANDLE)buffer);
                    if (!CreateBuffer(hWnd, buffer, width, height)) {
                        EndPaint(hWnd, &ps);
                        break;
                    }
                }

                HDC hMbDC = mbGetLockedViewDC(view);
                if (hMbDC) {
                    BitBlt(buffer->hdcMem, ps.rcPaint.left, ps.rcPaint.top,
                        ps.rcPaint.right - ps.rcPaint.left,
                        ps.rcPaint.bottom - ps.rcPaint.top,
                        hMbDC, ps.rcPaint.left, ps.rcPaint.top, SRCCOPY);
                    mbUnlockViewDC(view);
                }

                BitBlt(hdc, ps.rcPaint.left, ps.rcPaint.top,
                    ps.rcPaint.right - ps.rcPaint.left,
                    ps.rcPaint.bottom - ps.rcPaint.top,
                    buffer->hdcMem, ps.rcPaint.left, ps.rcPaint.top, SRCCOPY);

                EndPaint(hWnd, &ps);
                return 1;
            }
            break;

        case WM_SIZE:
        {
            if (wParam == SIZE_MINIMIZED) {
                return 0;
            }

            RECT rc;
            GetClientRect(hWnd, &rc);
            int width = rc.right - rc.left;
            int height = rc.bottom - rc.top;

            // 根据窗口状态优化处理
            if (wParam == SIZE_MAXIMIZED) {
                // 最大化时立即更新圆角（移除圆角）
                UpdateWindowRoundCorner(hWnd);
                OutputDebugStringA("WM_SIZE: Window maximized, corners removed\n");
            } else if (wParam == SIZE_RESTORED) {
                // 还原时延迟更新圆角以获得更流畅的动画
                SetTimer(hWnd, 3, 100, NULL); // 100ms延迟，给动画更多时间
                OutputDebugStringA("WM_SIZE: Window restored, delayed corner update\n");
            } else {
                // 其他情况使用标准延迟
                SetTimer(hWnd, 3, 50, NULL);
            }

            if (buffer && (buffer->width != width || buffer->height != height)) {
                DeleteBuffer(buffer);
                if (!CreateBuffer(hWnd, buffer, width, height)) break;
            }
            mbResize(view, width, height);
            mbWake(view);
            return 0;
        }

        case WM_SYSCOMMAND:
        {
            if (wParam == SC_RESTORE) {
                // 窗口恢复时，标记需要重绘并延迟更新圆角
                if (buffer) {
                    buffer->needsFullRedraw = true;
                }
                SetTimer(hWnd, 3, 100, NULL); // 延迟更新圆角
                OutputDebugStringA("WM_SYSCOMMAND: SC_RESTORE\n");
            }
            else if (wParam == SC_MAXIMIZE) {
                // 窗口最大化时，立即更新圆角（移除圆角）
                OutputDebugStringA("WM_SYSCOMMAND: SC_MAXIMIZE\n");
                // 让系统处理最大化，然后在WM_SIZE中处理圆角
            }
            else if (wParam == SC_MINIMIZE) {
                ShowWindow(hWnd, SW_MINIMIZE);
                return 0;
            }
            return DefWindowProc(hWnd, msg, wParam, lParam);
        }

        case WM_TIMER:
            if (wParam == 1) {
                BufferInfo* buffer = (BufferInfo*)GetProp(hWnd, L"buffer");
                if (buffer && buffer->hUpdateRgn) {
                    InvalidateRgn(hWnd, buffer->hUpdateRgn, FALSE);
                    DeleteObject(buffer->hUpdateRgn);
                    buffer->hUpdateRgn = NULL;
                    KillTimer(hWnd, buffer->timerId);
                    buffer->timerId = 0;
                }
            }
            else if (wParam == 2) {
                // 处理从最小化恢复的延迟重绘
                KillTimer(hWnd, 2);
                if (buffer) {
                    RECT rc;
                    GetClientRect(hWnd, &rc);
                    buffer->needsFullRedraw = true;
                    InvalidateRect(hWnd, &rc, FALSE);
                    mbWake(view);
                }
            }
            else if (wParam == 3) {
                // 延迟圆角更新定时器
                KillTimer(hWnd, 3);
                UpdateWindowRoundCorner(hWnd);
                OutputDebugStringA("WM_TIMER: Delayed corner update completed\n");
            }
            return 0;

        case WM_KEYDOWN:
        case WM_KEYUP:
        case WM_CHAR:
        {
            unsigned int flags = 0;
            if (HIWORD(lParam) & KF_REPEAT) flags |= MB_REPEAT;
            if (HIWORD(lParam) & KF_EXTENDED) flags |= MB_EXTENDED;
            if (msg == WM_KEYDOWN && mbFireKeyDownEvent(view, wParam, flags, FALSE)) return 0;
            if (msg == WM_KEYUP && mbFireKeyUpEvent(view, wParam, flags, FALSE)) return 0;
            if (msg == WM_CHAR && mbFireKeyPressEvent(view, wParam, flags, FALSE)) return 0;
            break;
        }

        case WM_LBUTTONDOWN:
        case WM_MBUTTONDOWN:
        case WM_RBUTTONDOWN:
        case WM_LBUTTONDBLCLK:
        case WM_MBUTTONDBLCLK:
        case WM_RBUTTONDBLCLK:
        case WM_LBUTTONUP:
        case WM_MBUTTONUP:
        case WM_RBUTTONUP:
        case WM_MOUSEMOVE:
        {
            int x = GET_X_LPARAM(lParam);
            int y = GET_Y_LPARAM(lParam);

            // 注销拖动功能相关代码
            /*
            if (msg == WM_LBUTTONDOWN) {
                LRESULT hitTest = SendMessage(hWnd, WM_NCHITTEST, 0, lParam);
                if (hitTest >= HTLEFT && hitTest <= HTBOTTOMRIGHT) return DefWindowProc(hWnd, msg, wParam, lParam);
            }
            */

            unsigned int flags = 0;
            if (wParam & MK_CONTROL) flags |= MB_CONTROL;
            if (wParam & MK_SHIFT) flags |= MB_SHIFT;
            if (wParam & MK_LBUTTON) flags |= MB_LBUTTON;
            if (wParam & MK_MBUTTON) flags |= MB_MBUTTON;
            if (wParam & MK_RBUTTON) flags |= MB_RBUTTON;

            if (mbFireMouseEvent(view, msg, x, y, flags)) return 0;

            if (msg == WM_LBUTTONDOWN || msg == WM_MBUTTONDOWN || msg == WM_RBUTTONDOWN) SetCapture(hWnd);
            else if (msg == WM_LBUTTONUP || msg == WM_MBUTTONUP || msg == WM_RBUTTONUP) ReleaseCapture();
            return 0;
        }

        case WM_CONTEXTMENU:
        {
            POINT pt = { LOWORD(lParam), HIWORD(lParam) };
            if (pt.x != -1 && pt.y != -1) ScreenToClient(hWnd, &pt);

            unsigned int flags = 0;
            if (wParam & MK_CONTROL) flags |= MB_CONTROL;
            if (wParam & MK_SHIFT) flags |= MB_SHIFT;
            if (wParam & MK_LBUTTON) flags |= MB_LBUTTON;
            if (wParam & MK_MBUTTON) flags |= MB_MBUTTON;
            if (wParam & MK_RBUTTON) flags |= MB_RBUTTON;

            if (mbFireContextMenuEvent(view, pt.x, pt.y, flags)) return 0;
            break;
        }

        case WM_MOUSEWHEEL:
        {
            POINT pt = { LOWORD(lParam), HIWORD(lParam) };
            ScreenToClient(hWnd, &pt);
            int delta = GET_WHEEL_DELTA_WPARAM(wParam);

            unsigned int flags = 0;
            if (wParam & MK_CONTROL) flags |= MB_CONTROL;
            if (wParam & MK_SHIFT) flags |= MB_SHIFT;
            if (wParam & MK_LBUTTON) flags |= MB_LBUTTON;
            if (wParam & MK_MBUTTON) flags |= MB_MBUTTON;
            if (wParam & MK_RBUTTON) flags |= MB_RBUTTON;

            if (mbFireMouseWheelEvent(view, pt.x, pt.y, delta, flags)) return 0;
            break;
        }

        case WM_SETFOCUS:
            mbSetFocus(view);
            return 0;

        case WM_KILLFOCUS:
            mbKillFocus(view);
            return 0;

        case WM_CLOSE:
            // 清理webview资源
            if (view) {
                mbDestroyWebView(view);
                RemoveProp(hWnd, L"mb");
            }
            // 清理缓冲区资源
            if (buffer) {
                if (buffer->hUpdateRgn) DeleteObject(buffer->hUpdateRgn);
                if (buffer->timerId) KillTimer(hWnd, buffer->timerId);
                DeleteBuffer(buffer);
                delete buffer;
                RemoveProp(hWnd, L"buffer");
            }
            // 清理工作区最大化标记
            if (GetProp(hWnd, L"WorkAreaMaximized")) RemoveProp(hWnd, L"WorkAreaMaximized");
            DestroyWindow(hWnd);
            // 完全退出进程
            PostQuitMessage(0);
            ExitProcess(0);
            return 0;

        // 注销拖动功能 - 禁用拖拽光标设置
        /*
        case WM_SETCURSOR:
            switch (LOWORD(lParam)) {
            case HTLEFT:
            case HTRIGHT:
                SetCursor(LoadCursor(NULL, IDC_SIZEWE));
                return TRUE;
            case HTTOP:
            case HTBOTTOM:
                SetCursor(LoadCursor(NULL, IDC_SIZENS));
                return TRUE;
            case HTTOPLEFT:
            case HTBOTTOMRIGHT:
                SetCursor(LoadCursor(NULL, IDC_SIZENWSE));
                return TRUE;
            case HTTOPRIGHT:
            case HTBOTTOMLEFT:
                SetCursor(LoadCursor(NULL, IDC_SIZENESW));
                return TRUE;
            case HTCLIENT:
                SetCursor(LoadCursor(NULL, IDC_ARROW));
                return TRUE;
            }
            break;
        */

        case WM_IME_STARTCOMPOSITION:
            if (mbFireWindowsMessage(view, hWnd, WM_IME_STARTCOMPOSITION, 0, 0, &result)) return result;
            break;

        // 注销拖动功能 - 禁用窗口拖动和边框拉伸
        /*
        case WM_NCHITTEST:
        {
            POINT pt = { GET_X_LPARAM(lParam), GET_Y_LPARAM(lParam) };
            RECT rcWindow;
            GetWindowRect(hWnd, &rcWindow);
            const int frameWidth = 8;

            bool bOnLeft = (pt.x >= rcWindow.left && pt.x <= rcWindow.left + frameWidth);
            bool bOnRight = (pt.x >= rcWindow.right - frameWidth && pt.x <= rcWindow.right);
            bool bOnTop = (pt.y >= rcWindow.top && pt.y <= rcWindow.top + frameWidth);
            bool bOnBottom = (pt.y >= rcWindow.bottom - frameWidth && pt.y <= rcWindow.bottom);

            // 仅处理边框拉伸，移除标题栏识别
            if (bOnLeft && bOnTop) return HTTOPLEFT;
            if (bOnLeft && bOnBottom) return HTBOTTOMLEFT;
            if (bOnRight && bOnTop) return HTTOPRIGHT;
            if (bOnRight && bOnBottom) return HTBOTTOMRIGHT;
            if (bOnLeft) return HTLEFT;
            if (bOnRight) return HTRIGHT;
            if (bOnTop) return HTTOP;
            if (bOnBottom) return HTBOTTOM;

            return HTCLIENT;
        }
        */

        case WM_GETMINMAXINFO:
        {
            LPMINMAXINFO lpMMI = (LPMINMAXINFO)lParam;
            lpMMI->ptMinTrackSize.x = MIN_WINDOW_WIDTH;
            lpMMI->ptMinTrackSize.y = MIN_WINDOW_HEIGHT;
            return 0;
        }

        case WM_ACTIVATE:
        {
            if (LOWORD(wParam) == WA_CLICKACTIVE) {
                if (IsIconic(hWnd)) {
                    ShowWindow(hWnd, SW_RESTORE);   // 先还原窗口
                    MaximizeToWorkArea(hWnd);       // 然后工作区最大化
                }
            }
            return DefWindowProc(hWnd, msg, wParam, lParam);
        }

        case WM_NCACTIVATE:
        {
            if (IsIconic(hWnd)) {
                return TRUE;
            }
            return DefWindowProc(hWnd, msg, wParam, lParam);
        }

        case WM_NCLBUTTONDOWN:
        {
            if (wParam == HTMINBUTTON) {
                ShowWindow(hWnd, SW_MINIMIZE);
                return 0;
            }
            return DefWindowProc(hWnd, msg, wParam, lParam);
        }

        case WM_NCLBUTTONDBLCLK:
        {
            if (IsIconic(hWnd)) {
                ShowWindow(hWnd, SW_RESTORE);   // 先还原窗口
                MaximizeToWorkArea(hWnd);       // 然后工作区最大化
                return 0;
            }

            // 移除标题栏双击最大化/还原功能
            return DefWindowProc(hWnd, msg, wParam, lParam);
        }

        case WM_WINDOWPOSCHANGED:
        {
            WINDOWPOS* wp = (WINDOWPOS*)lParam;
            if (wp->flags & SWP_NOSIZE) break;

            // 检测窗口状态变化
            static bool wasMaximized = false;
            bool isMaximized = IsZoomed(hWnd) != 0 || IsWorkAreaMaximized(hWnd);

            if (isMaximized != wasMaximized) {
                // 窗口状态发生变化（最大化<->还原），立即更新圆角
                UpdateWindowRoundCorner(hWnd);
                wasMaximized = isMaximized;
                OutputDebugStringA(isMaximized ? "WM_WINDOWPOSCHANGED: Maximized\n" : "WM_WINDOWPOSCHANGED: Restored\n");
            } else if (wp->flags & SWP_FRAMECHANGED) {
                // 框架变化时立即更新
                UpdateWindowRoundCorner(hWnd);
            } else {
                // 其他情况使用延迟更新
                SetTimer(hWnd, 3, 50, NULL);
            }
            break;
        }
        }
        return DefWindowProc(hWnd, msg, wParam, lParam);
    }

    bool regWndClass(LPCTSTR lpcsClassName, DWORD dwStyle) {
        WNDCLASS wndclass = { 0 };
        wndclass.style = dwStyle | CS_HREDRAW | CS_VREDRAW | CS_DBLCLKS;
        wndclass.lpfnWndProc = testWindowProc;
        wndclass.cbClsExtra = 200;
        wndclass.cbWndExtra = 200;
        wndclass.hInstance = GetModuleHandle(NULL);
        wndclass.hbrBackground = NULL;
        wndclass.lpszClassName = lpcsClassName;
        return RegisterClass(&wndclass) != 0;
    }

    bool unregWndClass(LPCTSTR lpcsClassName) {
        return UnregisterClass(lpcsClassName, GetModuleHandle(NULL)) != 0;
    }

} // namespace

//=============================================================================
// Public Interface
//=============================================================================

void createSimpleMb() {
    // 首先启用高DPI支持
    mbEnableHighDPISupport();

    if (!regWndClass(kClassWindow, CS_HREDRAW | CS_VREDRAW)) {
        OutputDebugStringA("Failed to register window class\n");
        return;
    }

    mbWebView view = mbCreateWebView();
    if (!view) {
        OutputDebugStringA("Failed to create web view\n");
        unregWndClass(kClassWindow);
        return;
    }

    // 设置磁盘缓存相关配置
    // 使用安全的方式调用API，避免空指针异常

    // 尝试启用磁盘缓存
    if (mbSetDiskCacheEnabled) {
        mbSetDiskCacheEnabled(view, TRUE);
        OutputDebugStringA("Disk cache enabled\n");
    } else {
        OutputDebugStringA("Warning: mbSetDiskCacheEnabled function not found\n");
    }

    // 尝试设置磁盘缓存大小限制为100MB
    const size_t cacheSizeLimit = 100 * 1024 * 1024;
    if (mbSetDiskCacheLimitDisk) {
        mbSetDiskCacheLimitDisk(view, cacheSizeLimit);
        OutputDebugStringA("Disk cache limit set to 100MB\n");
    } else {
        // 尝试使用替代函数
        if (mbSetDiskCacheLimit) {
            mbSetDiskCacheLimit(view, cacheSizeLimit);
            OutputDebugStringA("Disk cache limit set to 100MB using mbSetDiskCacheLimit\n");
        } else {
            OutputDebugStringA("Warning: Neither mbSetDiskCacheLimitDisk nor mbSetDiskCacheLimit function found\n");
        }
    }

    // 获取主屏幕尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // 确保获取到有效的屏幕尺寸
    if (screenWidth <= 0 || screenHeight <= 0) {
        screenWidth = 1024;  // 默认值
        screenHeight = 768;
        OutputDebugStringA("Warning: Failed to get screen metrics, using default values\n");
    }

    // 计算窗口大小（78%的屏幕大小）
    int windowWidth = static_cast<int>(screenWidth * 0.78);
    int windowHeight = static_cast<int>(screenHeight * 0.78);

    // 确保窗口大小不小于最小值
    windowWidth = max(windowWidth, MIN_WINDOW_WIDTH);
    windowHeight = max(windowHeight, MIN_WINDOW_HEIGHT);

    // 计算居中位置
    int x = (screenWidth - windowWidth) / 2;
    int y = (screenHeight - windowHeight) / 2;

    // 确保位置不为负
    x = max(x, 0);
    y = max(y, 0);

    // 创建窗口（不使用系统最大化，稍后使用工作区最大化）
    HWND hWnd = CreateWindowEx(
        WS_EX_APPWINDOW,
        kClassWindow,
        L"",  // 空标题
        WS_POPUP | WS_CLIPCHILDREN | WS_CLIPSIBLINGS | WS_SYSMENU | WS_MINIMIZEBOX | WS_THICKFRAME,
        x, y, windowWidth, windowHeight,
        NULL, NULL, GetModuleHandle(NULL), NULL);
    if (!hWnd) {
        mbDestroyWebView(view);
        unregWndClass(kClassWindow);
        OutputDebugStringA("Failed to create window\n");
        return;
    }

    LONG_PTR exStyle = GetWindowLongPtr(hWnd, GWL_EXSTYLE);
    SetWindowLongPtr(hWnd, GWL_EXSTYLE, exStyle | WS_EX_COMPOSITED);

    SetProp(hWnd, L"mb", (HANDLE)view);
    mbSetHandle(view, hWnd);

    // 设置圆角和阴影效果
    UpdateWindowRoundCorner(hWnd);

    mbOnPaintUpdated(view, handlePaintUpdatedCallback, hWnd);
    mbOnDocumentReady(view, handleDocumentReady, (void*)view);
    mbOnLoadingFinish(view, handleLoadingFinish, (void*)view);
    mbOnCreateView(view, handleCreateView, (void*)view);
    mbSetNavigationToNewWindowEnable(view, true);
    mbSetCspCheckEnable(view, false);
    mbEnableHighDPISupport();
    mbSetContextMenuEnabled(view, false);  // 禁用右键菜单

    RECT rc;
    GetClientRect(hWnd, &rc);
    mbResize(view, rc.right - rc.left, rc.bottom - rc.top);

    WCHAR exePath[MAX_PATH] = { 0 };
    if (!GetModuleFileNameW(NULL, exePath, MAX_PATH) || !PathRemoveFileSpecW(exePath)) {
        OutputDebugStringA("Failed to get executable path\n");
        return;
    }

    WCHAR filePath[MAX_PATH] = { 0 };
    PathCombineW(filePath, exePath, L"index.html");

    std::wstring url = L"file:///";
    for (int i = 0; filePath[i]; ++i) {
        url += (filePath[i] == L'\\') ? L'/' : filePath[i];
    }

    int utf8Len = WideCharToMultiByte(CP_UTF8, 0, url.c_str(), -1, NULL, 0, NULL, NULL);
    if (utf8Len <= 0) {
        OutputDebugStringA("Failed to convert URL to UTF-8\n");
        return;
    }

    std::vector<char> utf8Url(utf8Len);
    WideCharToMultiByte(CP_UTF8, 0, url.c_str(), -1, utf8Url.data(), utf8Len, NULL, NULL);

    mbLoadURL(view, utf8Url.data());
    mbOnJsQuery(view, onJsQuery, (void*)1);

    Sleep(1000); // Consider replacing with proper event handling

    // 移除冗余的最大化调用 - handleLoadingFinish中已经设置了最大化
}