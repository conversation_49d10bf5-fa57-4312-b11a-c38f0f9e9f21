﻿C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v150\Platforms\x64\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  main.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\apifunc.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\websocket\websocketserver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\frame.hpp(834): warning C4267: “=”: 从“size_t”转换到“uint32_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2039: “is_same”: 不是“boost”的成员
  e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\boost\type_traits\aligned_storage.hpp(25): note: 参见“boost”的声明
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2873: “is_same”: 符号不能用在 using 声明中
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\md5.hpp(367): warning C4267: “+=”: 从“size_t”转换到“websocketpp::md5::md5_word_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\sha1\sha1.hpp(176): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\dllrouter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\database\taskdata.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  mb_window.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp(17): warning C4005: “MIN_WINDOW_WIDTH”: 宏重定义
  c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.h(8): note: 参见“MIN_WINDOW_WIDTH”的前一个定义
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp(18): warning C4005: “MIN_WINDOW_HEIGHT”: 宏重定义
  c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.h(9): note: 参见“MIN_WINDOW_HEIGHT”的前一个定义
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp(408): warning C4244: “初始化”: 从“WPARAM”转换到“unsigned int”，可能丢失数据
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp(421): warning C4244: “初始化”: 从“WPARAM”转换到“unsigned int”，可能丢失数据
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\mbwindow\mb_window.cpp(434): warning C4244: “初始化”: 从“WPARAM”转换到“unsigned int”，可能丢失数据
  WebSocketServer.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\websocket\websocketserver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\frame.hpp(834): warning C4267: “=”: 从“size_t”转换到“uint32_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2039: “is_same”: 不是“boost”的成员
  e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\boost\type_traits\aligned_storage.hpp(25): note: 参见“boost”的声明
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2873: “is_same”: 符号不能用在 using 声明中
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\md5.hpp(367): warning C4267: “+=”: 从“size_t”转换到“websocketpp::md5::md5_word_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\sha1\sha1.hpp(176): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\dllrouter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\database\taskdata.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  APIFunc.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\apifunc.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\websocket\websocketserver.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\frame.hpp(834): warning C4267: “=”: 从“size_t”转换到“uint32_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2039: “is_same”: 不是“boost”的成员
  e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\boost\type_traits\aligned_storage.hpp(25): note: 参见“boost”的声明
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\type_traits.hpp(59): error C2873: “is_same”: 符号不能用在 using 声明中
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\common\md5.hpp(367): warning C4267: “+=”: 从“size_t”转换到“websocketpp::md5::md5_word_t”，可能丢失数据
e:\vcpkg-2025.01.13\vcpkg-2025.01.13\installed\x64-windows\include\websocketpp\sha1\sha1.hpp(176): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\dllrouter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\database\taskdata.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  DLLRouter.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\dllrouter.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\apifunc\dllrouter.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\database\taskdata.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  DatabaseManager.cpp
c:\users\<USER>\desktop\frontend+backend 对接文件复\frontend+backend 修改排版\frontend+backend 2.27\backend\database\taskdata.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
