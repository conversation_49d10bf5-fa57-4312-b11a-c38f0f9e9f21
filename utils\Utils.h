#pragma once

#include <string>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <random>
#include <Windows.h>
#include <direct.h> // for _mkdir
#include <io.h> // for _access

namespace Utils {
    // 获取当前时间戳
    inline std::string getCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        std::tm tm_buf;
        localtime_s(&tm_buf, &time);
        std::stringstream ss;
        ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }

    // 生成唯一ID
    inline std::string generateUniqueId() {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(0, 15);
        static const char* hex = "0123456789abcdef";

        std::string uuid;
        uuid.reserve(32);
        for (int i = 0; i < 32; ++i) {
            uuid += hex[dis(gen)];
        }
        return uuid;
    }

    // 宽字符到UTF-8转换函数
    inline std::string wideToUtf8(const std::wstring& wideStr) {
        if (wideStr.empty()) {
            return std::string();
        }

        int utf8Len = WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, NULL, 0, NULL, NULL);
        if (utf8Len <= 0) {
            return std::string();
        }

        std::vector<char> utf8Buffer(utf8Len);
        WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, utf8Buffer.data(), utf8Len, NULL, NULL);

        return std::string(utf8Buffer.data());
    }

    // UTF-8到宽字符转换函数
    inline std::wstring utf8ToWide(const std::string& utf8Str) {
        if (utf8Str.empty()) {
            return std::wstring();
        }

        int wideLen = MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, NULL, 0);
        if (wideLen <= 0) {
            return std::wstring();
        }

        std::vector<wchar_t> wideBuffer(wideLen);
        MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, wideBuffer.data(), wideLen);

        return std::wstring(wideBuffer.data());
    }

    // 获取应用程序基础路径（UTF-8编码安全版本）
    inline std::string getBasePathUtf8() {
        wchar_t modulePath[MAX_PATH];
        GetModuleFileNameW(NULL, modulePath, MAX_PATH);
        std::wstring wideExePath(modulePath);
        std::string utf8ExePath = wideToUtf8(wideExePath);
        return utf8ExePath.substr(0, utf8ExePath.find_last_of("\\") + 1);
    }

    // 获取应用程序基础路径（保持向后兼容）
    inline std::string getBasePath() {
        char modulePath[MAX_PATH];
        GetModuleFileNameA(NULL, modulePath, MAX_PATH);
        std::string exePath(modulePath);
        return exePath.substr(0, exePath.find_last_of("\\") + 1);
    }

    // 获取插件目录路径（UTF-8编码安全版本）
    inline std::string getPluginPathUtf8() {
        std::string basePath = getBasePathUtf8();
        std::string pluginPath = basePath + "plugin\\";

        // 确保plugin目录存在（使用UTF-8安全的方式）
        std::wstring widePluginPath = utf8ToWide(pluginPath);
        if (_waccess(widePluginPath.c_str(), 0) != 0) {
            _wmkdir(widePluginPath.c_str());
        }

        return pluginPath;
    }

    // 获取插件目录路径（保持向后兼容）
    inline std::string getPluginPath() {
        std::string basePath = getBasePath();
        std::string pluginPath = basePath + "plugin\\";

        // 确保plugin目录存在
        if (_access(pluginPath.c_str(), 0) != 0) {
            _mkdir(pluginPath.c_str());
        }

        return pluginPath;
    }

    // 确保目录存在（UTF-8编码安全版本）
    inline bool ensureDirectoryExistsUtf8(const std::string& utf8Path) {
        std::string dir = utf8Path;
        // 如果是文件路径，提取目录部分
        size_t lastSlash = utf8Path.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            dir = utf8Path.substr(0, lastSlash);
        }

        if (!dir.empty()) {
            // 转换为宽字符
            std::wstring wideDir = utf8ToWide(dir);
            if (_waccess(wideDir.c_str(), 0) != 0) {
                return _wmkdir(wideDir.c_str()) == 0;
            }
        }
        return true;
    }

    // 确保目录存在（保持向后兼容）
    inline bool ensureDirectoryExists(const std::string& path) {
        std::string dir = path;
        // 如果是文件路径，提取目录部分
        size_t lastSlash = path.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            dir = path.substr(0, lastSlash);
        }

        if (!dir.empty() && _access(dir.c_str(), 0) != 0) {
            return _mkdir(dir.c_str()) == 0;
        }
        return true;
    }

    // 规范化路径 - 移除../和./等特殊路径元素
    inline std::string normalizePath(const std::string& path) {
        char canonicalPath[MAX_PATH];
        char* filePart = nullptr;

        if (!GetFullPathNameA(path.c_str(), MAX_PATH, canonicalPath, &filePart)) {
            return "";
        }

        return std::string(canonicalPath);
    }

    // 检查路径是否安全 - 确保不会通过../等方式逃离预定目录
    inline bool isPathSafe(const std::string& path, const std::string& basePath) {
        std::string normalizedPath = normalizePath(path);
        std::string normalizedBasePath = normalizePath(basePath);

        if (normalizedPath.empty() || normalizedBasePath.empty()) {
            return false; // 路径无效
        }

        // 检查normalizedPath是否以normalizedBasePath开头
        return normalizedPath.find(normalizedBasePath) == 0;
    }

    // 获取安全路径 - 确保路径在指定目录内
    inline std::string getSafePath(const std::string& inputPath, const std::string& basePath) {
        // 如果是绝对路径，先检查是否在安全目录内
        if (inputPath.find(":") != std::string::npos) {
            if (isPathSafe(inputPath, basePath)) {
                return inputPath;
            }
            // 绝对路径但不安全，回退到基础目录
            return basePath + inputPath.substr(inputPath.find_last_of("\\") + 1);
        }

        // 对于相对路径，确保它们在基础目录内
        std::string fullPath = basePath + inputPath;

        // 检查构建的路径是否安全
        if (isPathSafe(fullPath, basePath)) {
            return fullPath;
        }

        // 如果路径包含..或其他不安全成分，只使用文件名
        std::string fileName = inputPath;
        size_t lastSlash = inputPath.find_last_of("/\\");
        if (lastSlash != std::string::npos) {
            fileName = inputPath.substr(lastSlash + 1);
        }

        return basePath + fileName;
    }
}
