@echo off
echo 正在编译带有阴影效果的程序...
echo.

REM 检查是否存在Visual Studio环境
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Visual Studio编译器
    echo 请先运行Visual Studio开发者命令提示符，或者安装Visual Studio
    echo.
    echo 如果已安装Visual Studio，请运行以下命令之一：
    echo   - "Developer Command Prompt for VS 2019"
    echo   - "Developer Command Prompt for VS 2022"
    echo   - 或者运行: "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    echo.
    pause
    exit /b 1
)

echo 找到Visual Studio编译器，开始编译...
echo.

REM 使用MSBuild编译项目
msbuild fastmb.vcxproj /p:Configuration=Release /p:Platform=x64 /v:minimal

if %errorlevel% equ 0 (
    echo.
    echo ✓ 编译成功！
    echo.
    echo 新的可执行文件位置: Release\fastmb.exe
    echo 或者: x64\Release\fastmb.exe
    echo.
    echo 现在可以运行程序测试阴影效果了。
    echo.
    echo 测试步骤：
    echo 1. 运行 Release\fastmb.exe
    echo 2. 观察窗口周围是否有阴影
    echo 3. 尝试将窗口拖到不同颜色的背景上
    echo 4. 测试最大化和还原功能
    echo.
) else (
    echo.
    echo ✗ 编译失败！
    echo.
    echo 可能的解决方案：
    echo 1. 检查是否缺少依赖库
    echo 2. 确保所有源文件都存在
    echo 3. 检查vcpkg依赖是否正确安装
    echo.
)

pause
