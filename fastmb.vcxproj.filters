﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Resources">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="WebSocket">
      <UniqueIdentifier>{8E0D8C7F-D123-4A76-B77E-123456789ABC}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
    <Filter Include="src">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
    <Filter Include="APIFunc">
      <UniqueIdentifier>{A1B2C3D4-E5F6-4G7H-I8J9-KLMNOPQRSTUV}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
    <Filter Include="mbwindow">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
    <Filter Include="database">
      <UniqueIdentifier>{5FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
    <Filter Include="utils">
      <UniqueIdentifier>{6FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;h</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="mbwindow\mb_window.cpp">
      <Filter>mbwindow</Filter>
    </ClCompile>
    <ClCompile Include="WebSocket\WebSocketServer.cpp">
      <Filter>WebSocket</Filter>
    </ClCompile>
    <ClCompile Include="APIFunc\APIFunc.cpp">
      <Filter>APIFunc</Filter>
    </ClCompile>
    <ClCompile Include="APIFunc\DLLRouter.cpp">
      <Filter>APIFunc</Filter>
    </ClCompile>
    <ClCompile Include="database\DatabaseManager.cpp">
      <Filter>database</Filter>
    </ClCompile>
    <ClCompile Include="database\JsonSchemaProcessor.cpp">
      <Filter>database</Filter>
    </ClCompile>
    <ClCompile Include="utils\MemoryDataManager.cpp">
      <Filter>utils</Filter>
    </ClCompile>

  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="mbwindow\mb_window.h">
      <Filter>mbwindow</Filter>
    </ClInclude>
    <ClInclude Include="mbwindow\mb.h">
      <Filter>mbwindow</Filter>
    </ClInclude>
    <ClInclude Include="WebSocket\WebSocketServer.h">
      <Filter>WebSocket</Filter>
    </ClInclude>
    <ClInclude Include="APIFunc\APIFunc.h">
      <Filter>APIFunc</Filter>
    </ClInclude>
    <ClInclude Include="APIFunc\DLLRouter.h">
      <Filter>APIFunc</Filter>
    </ClInclude>
    <ClInclude Include="database\DatabaseManager.h">
      <Filter>database</Filter>
    </ClInclude>
    <ClInclude Include="database\TaskData.h">
      <Filter>database</Filter>
    </ClInclude>
    <ClInclude Include="database\JsonSchemaProcessor.h">
      <Filter>database</Filter>
    </ClInclude>
    <ClInclude Include="utils\Logger.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\Exceptions.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\Utils.h">
      <Filter>utils</Filter>
    </ClInclude>
    <ClInclude Include="utils\MemoryDataManager.h">
      <Filter>utils</Filter>
    </ClInclude>
  </ItemGroup>
</Project>