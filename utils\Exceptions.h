#pragma once

#include <string>
#include <exception>

// 基础异常类
class AppException : public std::exception {
protected:
    std::string message_;
    std::string errorCode_;
    
public:
    AppException(const std::string& message, const std::string& errorCode = "APP_ERROR") 
        : message_(message), errorCode_(errorCode) {}
    
    const char* what() const noexcept override {
        return message_.c_str();
    }
    
    const std::string& errorCode() const {
        return errorCode_;
    }
};

// DLL路由相关异常
class DLLRouterException : public AppException {
public:
    DLLRouterException(const std::string& message, const std::string& errorCode = "DLL_ROUTER_ERROR") 
        : AppException(message, errorCode) {}
};

// 数据库相关异常
class DatabaseException : public AppException {
public:
    DatabaseException(const std::string& message, const std::string& errorCode = "DATABASE_ERROR") 
        : AppException(message, errorCode) {}
};

// 任务相关异常
class TaskException : public AppException {
public:
    TaskException(const std::string& message, const std::string& errorCode = "TASK_ERROR") 
        : AppException(message, errorCode) {}
};

// WebSocket相关异常
class WebSocketException : public AppException {
public:
    WebSocketException(const std::string& message, const std::string& errorCode = "WEBSOCKET_ERROR") 
        : AppException(message, errorCode) {}
};
