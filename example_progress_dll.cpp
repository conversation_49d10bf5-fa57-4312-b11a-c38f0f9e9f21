/**
 * 示例DLL文件，演示如何使用进度回调
 * 编译命令: cl /LD example_progress_dll.cpp /link /OUT:progress_example.dll
 */

#include <Windows.h>
#include <string>
#include <thread>
#include <chrono>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

// 进度回调函数类型
typedef void (*ProgressCallback)(const std::string& taskId, int progress);

// 导出函数
extern "C" __declspec(dllexport) std::string __stdcall LongRunningTask(
    const std::string& params, 
    ProgressCallback progressCallback,
    const std::string& taskId
) {
    try {
        // 解析参数
        json j = json::parse(params);
        
        // 获取总步骤数（默认10步）
        int totalSteps = j.value("totalSteps", 10);
        
        // 获取每步延迟（默认500毫秒）
        int delayPerStep = j.value("delayPerStep", 500);
        
        // 执行长时间任务，并报告进度
        for (int step = 1; step <= totalSteps; ++step) {
            // 计算进度百分比
            int progress = (step * 100) / totalSteps;
            
            // 如果有回调函数，则报告进度
            if (progressCallback) {
                progressCallback(taskId, progress);
            }
            
            // 模拟工作延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(delayPerStep));
        }
        
        // 返回结果
        json result;
        result["status"] = "success";
        result["message"] = "任务已完成";
        result["totalSteps"] = totalSteps;
        result["taskId"] = taskId;
        
        return result.dump();
    }
    catch (const std::exception& e) {
        json error;
        error["status"] = "error";
        error["message"] = e.what();
        return error.dump();
    }
}

// DLL入口函数
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}
