#pragma once

#include <string>
#include <nlohmann/json.hpp>

struct TaskData {
    std::string taskId;        // 任务ID
    std::string dllName;       // DLL名称
    std::string funcName;      // 函数名称
    std::string dataType;      // 数据类型
    std::string content;       // 数据内容(JSON格式)
    int progress;             // 任务进度(0-100)
    std::string timestamp;    // 时间戳
    std::string params;       // 请求参数(JSON格式)

    // JSON序列化
    nlohmann::json toJson() const {
        return {
            {"taskId", taskId},
            {"dllName", dllName},
            {"funcName", funcName},
            {"dataType", dataType},
            {"content", content},
            {"progress", progress},
            {"timestamp", timestamp},
            {"params", params}
        };
    }

    // 从JSON反序列化
    static TaskData fromJson(const nlohmann::json& j) {
        TaskData data;
        data.taskId = j["taskId"].get<std::string>();

        // 处理可能不存在的新字段
        if (j.contains("dllName")) {
            data.dllName = j["dllName"].get<std::string>();
        }
        if (j.contains("funcName")) {
            data.funcName = j["funcName"].get<std::string>();
        }
        if (j.contains("params")) {
            data.params = j["params"].get<std::string>();
        }

        data.dataType = j["dataType"].get<std::string>();
        data.content = j["content"].get<std::string>();
        data.progress = j["progress"].get<int>();
        data.timestamp = j["timestamp"].get<std::string>();
        return data;
    }
};