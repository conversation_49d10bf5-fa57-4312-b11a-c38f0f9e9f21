<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
        }
        .json-response {
            white-space: pre-wrap;
            font-family: monospace;
        }
        .real-time-data {
            background-color: #e8f5e9;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .data-item {
            padding: 5px;
            border-bottom: 1px solid #ddd;
            animation: fadeIn 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }
        .progress {
            width: 0%;
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>WebSocket测试页面</h1>
    
    <div class="container">
        <h3>连接状态</h3>
        <div>
            <span id="status">未连接</span>
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开</button>
        </div>
    </div>

    <div class="container">
        <h3>长时间任务执行</h3>
        <div>
            <input type="text" id="taskInput" placeholder="输入任务参数">
            <button onclick="startLongTask()">开始任务</button>
            <button onclick="stopLongTask()">停止任务</button>
            <div class="progress-bar">
                <div id="taskProgress" class="progress"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>实时数据</h3>
        <div class="real-time-data" id="realTimeData"></div>
    </div>

    <div class="container">
        <h3>日志</h3>
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        let taskRunning = false;
        let currentTaskId = null;
        const statusElem = document.getElementById('status');
        const logElem = document.getElementById('log');
        const realTimeDataElem = document.getElementById('realTimeData');
        const progressElem = document.getElementById('taskProgress');

        function log(message, isJson = false) {
            const div = document.createElement('div');
            if (isJson) {
                div.className = 'json-response';
                div.textContent = `${new Date().toLocaleTimeString()} - ${JSON.stringify(message, null, 2)}`;
            } else {
                div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            }
            logElem.appendChild(div);
            logElem.scrollTop = logElem.scrollHeight;
        }

        function updateRealTimeData(data) {
            const div = document.createElement('div');
            div.className = 'data-item';
            div.textContent = `${new Date().toLocaleTimeString()} - ${JSON.stringify(data)}`;
            realTimeDataElem.insertBefore(div, realTimeDataElem.firstChild);
            
            // 保持最新的100条数据
            while (realTimeDataElem.children.length > 100) {
                realTimeDataElem.removeChild(realTimeDataElem.lastChild);
            }
        }

        function updateProgress(progress) {
            progressElem.style.width = `${progress}%`;
        }

        function connect() {
            if (ws) {
                log('已经连接');
                return;
            }

            ws = new WebSocket('ws://localhost:9002');
            
            ws.onopen = () => {
                statusElem.textContent = '已连接';
                log('连接成功');
            };
            
            ws.onclose = () => {
                statusElem.textContent = '未连接';
                log('连接关闭');
                ws = null;
                taskRunning = false;
                updateProgress(0);
            };
            
            ws.onerror = (error) => {
                log('错误: ' + error);
            };
            
            ws.onmessage = (event) => {
                try {
                    const response = JSON.parse(event.data);
                    
                    // 处理不同类型的消息
                    if (response.type === 'taskProgress') {
                        if (taskRunning) {  // 只在任务运行时更新进度
                            updateProgress(response.progress);
                            updateRealTimeData(response.data);
                            currentTaskId = response.taskId;
                        }
                    } else if (response.type === 'taskComplete') {
                        taskRunning = false;
                        currentTaskId = null;
                        log('任务完成');
                        updateProgress(100);
                    } else {
                        const result = JSON.parse(response.result || response);
                        if (result.taskId) {
                            currentTaskId = result.taskId;
                        }
                        // 处理停止任务的响应
                        if (result.message === "Task stopped") {
                            taskRunning = false;
                            currentTaskId = null;
                            updateProgress(0);
                            log('任务已停止');
                        }
                        log(response, true);
                    }
                } catch (e) {
                    log('收到: ' + event.data);
                }
            };
        }

        function disconnect() {
            if (!ws) {
                log('未连接');
                return;
            }
            ws.close();
        }

        function startLongTask() {
            if (!ws) {
                log('未连接');
                return;
            }
            if (taskRunning) {
                log('任务已在运行中');
                return;
            }
            
            const input = document.getElementById('taskInput');
            const message = {
                func: 'echo',
                params: JSON.stringify({
                    command: 'startLongTask',
                    data: {
                        params: input.value
                    }
                })
            };
            ws.send(JSON.stringify(message));
            taskRunning = true;
            updateProgress(0);
            log('开始执行长时间任务');
        }

        function stopLongTask() {
            if (!ws || !taskRunning) {
                log('没有正在运行的任务');
                return;
            }
            
            if (!currentTaskId) {
                log('无法停止任务：未找到任务ID');
                return;
            }
            
            const message = {
                func: 'echo',
                params: JSON.stringify({
                    command: 'stopTask',
                    data: {
                        taskId: currentTaskId
                    }
                })
            };
            ws.send(JSON.stringify(message));
            log('请求停止任务');
        }
    </script>
</body>
</html>