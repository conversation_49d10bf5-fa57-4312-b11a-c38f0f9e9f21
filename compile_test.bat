@echo off
echo 编译阴影测试程序...
echo.

REM 检查编译器
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Visual Studio编译器
    echo 请先运行Visual Studio开发者命令提示符
    pause
    exit /b 1
)

REM 编译测试程序
cl test_shadow.cpp user32.lib gdi32.lib dwmapi.lib /Fe:shadow_test.exe

if %errorlevel% equ 0 (
    echo.
    echo ✓ 编译成功！
    echo.
    echo 运行测试程序: shadow_test.exe
    echo.
    echo 如果看到阴影，说明您的系统支持阴影效果
    echo 如果没有阴影，可能需要：
    echo 1. 启用桌面合成效果
    echo 2. 更新显卡驱动
    echo 3. 检查Windows版本（需要XP SP2以上）
    echo.
    
    REM 自动运行测试程序
    start shadow_test.exe
) else (
    echo.
    echo ✗ 编译失败！
    echo.
)

pause
