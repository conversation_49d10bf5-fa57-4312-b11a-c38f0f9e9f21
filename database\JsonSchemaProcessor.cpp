#include "JsonSchemaProcessor.h"
#include <sstream>
#include <algorithm>
#include <cctype>
#include "../utils/Utils.h"

JsonSchemaProcessor::JsonSchemaProcessor() : running_(false) {
}

JsonSchemaProcessor::~JsonSchemaProcessor() {
    cleanup();
}

bool JsonSchemaProcessor::initialize() {
    try {
        LogInfo("Initializing JsonSchemaProcessor");

        // 启动处理线程
        running_ = true;
        processingThread_ = std::thread(&JsonSchemaProcessor::processingThread, this);

        LogInfo("JsonSchemaProcessor initialized successfully");
        return true;
    } catch (const std::exception& e) {
        setError("Exception during initialization: " + std::string(e.what()));
        return false;
    } catch (...) {
        setError("Unknown exception during initialization");
        return false;
    }
}

void JsonSchemaProcessor::cleanup() {
    try {
        LogInfo("Cleaning up JsonSchemaProcessor");

        // 停止处理线程
        running_ = false;
        queueCv_.notify_all();

        if (processingThread_.joinable()) {
            processingThread_.join();
        }

        // 清空队列
        {
            std::lock_guard<std::mutex> lock(queueMutex_);
            while (!taskQueue_.empty()) {
                taskQueue_.pop();
            }
        }

        // 清空表结构缓存
        {
            std::lock_guard<std::mutex> lock(schemaCacheMutex_);
            tableSchemaCache_.clear();
        }

        LogInfo("JsonSchemaProcessor cleanup completed");
    } catch (const std::exception& e) {
        setError("Exception during cleanup: " + std::string(e.what()));
    } catch (...) {
        setError("Unknown exception during cleanup");
    }
}

void JsonSchemaProcessor::submitTask(const JsonProcessTask& task) {
    try {
        // 将任务添加到队列
        {
            std::lock_guard<std::mutex> lock(queueMutex_);
            taskQueue_.push(task);
        }

        // 通知处理线程
        queueCv_.notify_one();

        LogInfo("JSON processing task submitted for " + task.dllName + "::" + task.funcName);
    } catch (const std::exception& e) {
        setError("Exception during task submission: " + std::string(e.what()));
    }
}

void JsonSchemaProcessor::processingThread() {
    LogInfo("JSON processing thread started");

    while (running_) {
        JsonProcessTask task;
        bool hasTask = false;

        // 等待任务
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            queueCv_.wait(lock, [this] { return !taskQueue_.empty() || !running_; });

            if (!running_) {
                break;
            }

            if (!taskQueue_.empty()) {
                task = taskQueue_.front();
                taskQueue_.pop();
                hasTask = true;
            }
        }

        // 处理任务
        if (hasTask) {
            try {
                processTask(task);
            } catch (const std::exception& e) {
                LogError("Exception during task processing: " + std::string(e.what()));
            } catch (...) {
                LogError("Unknown exception during task processing");
            }
        }
    }

    LogInfo("JSON processing thread stopped");
}

void JsonSchemaProcessor::processTask(const JsonProcessTask& task) {
    LogInfo("Processing JSON task for " + task.dllName + "::" + task.funcName);

    try {
        // 验证JSON格式
        json jsonData = json::parse(task.jsonContent);

        // 创建或更新表结构
        if (!createTableFromJson(task.dllName, task.funcName, task.jsonContent)) {
            LogWarning("Failed to create or update table for " + task.dllName + "::" + task.funcName);
            return;
        }

        // 插入数据
        if (!insertDataIntoTable(task.dllName, task.funcName, task.jsonContent, task.taskId, task.timestamp)) {
            LogWarning("Failed to insert data for " + task.dllName + "::" + task.funcName);
            return;
        }

        LogInfo("Successfully processed JSON task for " + task.dllName + "::" + task.funcName);
    } catch (const json::parse_error& e) {
        LogError("JSON parse error: " + std::string(e.what()) + " for " + task.dllName + "::" + task.funcName);
    } catch (const std::exception& e) {
        LogError("Exception during task processing: " + std::string(e.what()));
    }
}

bool JsonSchemaProcessor::createTableFromJson(const std::string& dllName, const std::string& funcName,
                                             const std::string& jsonContent) {
    try {
        // 解析JSON
        json jsonData = json::parse(jsonContent);

        // 生成表名
        std::string tableName = generateTableName(dllName, funcName);

        // 根据JSON类型选择处理方法
        if (jsonData.is_object()) {
            return createTableFromJsonObject(tableName, jsonData);
        } else if (jsonData.is_array() && !jsonData.empty()) {
            return createTableFromJsonArray(tableName, jsonData);
        } else {
            LogWarning("Unsupported JSON structure for table creation: " + tableName);
            return false;
        }
    } catch (const std::exception& e) {
        setError("Exception during table creation: " + std::string(e.what()));
        return false;
    }
}

bool JsonSchemaProcessor::createTableFromJsonObject(const std::string& tableName, const json& jsonObj) {
    try {
        // 收集字段和类型
        std::unordered_map<std::string, std::string> columns;

        // 字段名映射
        std::unordered_map<std::string, std::string> columnMapping;

        for (auto it = jsonObj.begin(); it != jsonObj.end(); ++it) {
            bool wasModified = false;
            std::string originalName = it.key();
            std::string columnName = sanitizeColumnName(originalName, &wasModified);
            std::string columnType = determineColumnType(it.value());

            // 跳过复杂类型
            if (columnType != "UNSUPPORTED") {
                columns[columnName] = columnType;

                // 如果列名被修改，保存映射关系
                if (wasModified) {
                    columnMapping[columnName] = originalName;
                    LogInfo("Column name mapping: " + originalName + " -> " + columnName);
                }
            }
        }

        // 检查表是否已存在
        bool tableExisted = tableExists(tableName);

        if (tableExisted) {
            // 获取现有表结构
            auto existingColumns = getTableColumns(tableName);

            // 更新表结构
            return updateTableStructure(tableName, existingColumns, columns);
        } else {
            // 创建新表
            std::stringstream createSql;
            createSql << "CREATE TABLE IF NOT EXISTS " << tableName << " (";
            createSql << "id INTEGER PRIMARY KEY AUTOINCREMENT,";
            createSql << "task_id TEXT NOT NULL,";
            createSql << "timestamp TEXT NOT NULL";

            for (const auto& col : columns) {
                createSql << ", " << col.first << " " << col.second;
            }

            createSql << ");";

            // 创建索引
            std::stringstream indexSql;
            indexSql << "CREATE INDEX IF NOT EXISTS idx_" << tableName << "_task_id ON "
                     << tableName << "(task_id);";

            // 执行SQL
            bool success = DatabaseManager::getInstance().executeQuery(createSql.str());
            if (success) {
                success = DatabaseManager::getInstance().executeQuery(indexSql.str());
            }

            // 更新缓存
            if (success) {
                {
                    std::lock_guard<std::mutex> lock(schemaCacheMutex_);
                    tableSchemaCache_[tableName] = columns;
                }

                // 保存列名映射
                if (!columnMapping.empty()) {
                    std::lock_guard<std::mutex> lock(mappingCacheMutex_);
                    columnMappingCache_[tableName] = columnMapping;
                    LogInfo("Saved " + std::to_string(columnMapping.size()) + " column mappings for table " + tableName);
                }
            }

            return success;
        }
    } catch (const std::exception& e) {
        setError("Exception during object table creation: " + std::string(e.what()));
        return false;
    }
}

bool JsonSchemaProcessor::createTableFromJsonArray(const std::string& tableName, const json& jsonArray) {
    try {
        // 合并所有数组元素的字段
        std::unordered_map<std::string, std::string> columns;

        // 处理数组中的第一个对象来确定基本结构
        if (jsonArray[0].is_object()) {
            // 字段名映射
            std::unordered_map<std::string, std::string> columnMapping;

            for (auto it = jsonArray[0].begin(); it != jsonArray[0].end(); ++it) {
                bool wasModified = false;
                std::string originalName = it.key();
                std::string columnName = sanitizeColumnName(originalName, &wasModified);
                std::string columnType = determineColumnType(it.value());

                // 跳过复杂类型
                if (columnType != "UNSUPPORTED") {
                    columns[columnName] = columnType;

                    // 如果列名被修改，保存映射关系
                    if (wasModified) {
                        columnMapping[columnName] = originalName;
                        LogInfo("Column name mapping: " + originalName + " -> " + columnName);
                    }
                }
            }

            // 扫描其余对象以查找额外的字段
            for (size_t i = 1; i < jsonArray.size(); ++i) {
                if (jsonArray[i].is_object()) {
                    for (auto it = jsonArray[i].begin(); it != jsonArray[i].end(); ++it) {
                        bool wasModified = false;
                        std::string originalName = it.key();
                        std::string columnName = sanitizeColumnName(originalName, &wasModified);

                        // 如果是新字段，添加到列表
                        if (columns.find(columnName) == columns.end()) {
                            std::string columnType = determineColumnType(it.value());
                            if (columnType != "UNSUPPORTED") {
                                columns[columnName] = columnType;

                                // 如果列名被修改，保存映射关系
                                if (wasModified) {
                                    columnMapping[columnName] = originalName;
                                    LogInfo("Column name mapping: " + originalName + " -> " + columnName);
                                }
                            }
                        }
                    }
                }
            }

            // 检查表是否已存在
            bool tableExisted = tableExists(tableName);

            if (tableExisted) {
                // 获取现有表结构
                auto existingColumns = getTableColumns(tableName);

                // 更新表结构
                return updateTableStructure(tableName, existingColumns, columns);
            } else {
                // 创建新表
                std::stringstream createSql;
                createSql << "CREATE TABLE IF NOT EXISTS " << tableName << " (";
                createSql << "id INTEGER PRIMARY KEY AUTOINCREMENT,";
                createSql << "task_id TEXT NOT NULL,";
                createSql << "timestamp TEXT NOT NULL";

                for (const auto& col : columns) {
                    createSql << ", " << col.first << " " << col.second;
                }

                createSql << ");";

                // 创建索引
                std::stringstream indexSql;
                indexSql << "CREATE INDEX IF NOT EXISTS idx_" << tableName << "_task_id ON "
                         << tableName << "(task_id);";

                // 执行SQL
                bool success = DatabaseManager::getInstance().executeQuery(createSql.str());
                if (success) {
                    success = DatabaseManager::getInstance().executeQuery(indexSql.str());
                }

                // 更新缓存
                if (success) {
                    {
                        std::lock_guard<std::mutex> lock(schemaCacheMutex_);
                        tableSchemaCache_[tableName] = columns;
                    }

                    // 保存列名映射
                    if (!columnMapping.empty()) {
                        std::lock_guard<std::mutex> lock(mappingCacheMutex_);
                        columnMappingCache_[tableName] = columnMapping;
                    }
                }

                return success;
            }
        } else {
            LogWarning("Array does not contain objects, cannot create table: " + tableName);
            return false;
        }
    } catch (const std::exception& e) {
        setError("Exception during array table creation: " + std::string(e.what()));
        return false;
    }
}

bool JsonSchemaProcessor::insertDataIntoTable(const std::string& dllName, const std::string& funcName,
                                             const std::string& jsonContent, const std::string& taskId,
                                             const std::string& timestamp) {
    try {
        // 解析JSON
        json jsonData = json::parse(jsonContent);

        // 生成表名
        std::string tableName = generateTableName(dllName, funcName);

        // 获取表结构
        std::unordered_map<std::string, std::string> columns;
        std::unordered_map<std::string, std::string> columnMapping;
        {
            std::lock_guard<std::mutex> lock(schemaCacheMutex_);
            auto it = tableSchemaCache_.find(tableName);
            if (it != tableSchemaCache_.end()) {
                columns = it->second;
            } else {
                columns = getTableColumns(tableName);
                tableSchemaCache_[tableName] = columns;
            }
        }

        // 获取列名映射
        {
            std::lock_guard<std::mutex> lock(mappingCacheMutex_);
            auto it = columnMappingCache_.find(tableName);
            if (it != columnMappingCache_.end()) {
                columnMapping = it->second;
            }
        }

        // 根据JSON类型选择处理方法
        if (jsonData.is_object()) {
            // 构建插入语句
            std::stringstream insertSql;
            insertSql << "INSERT INTO " << tableName << " (task_id, timestamp";

            for (const auto& col : columns) {
                insertSql << ", " << col.first;
            }

            insertSql << ") VALUES (?, ?";

            for (size_t i = 0; i < columns.size(); ++i) {
                insertSql << ", ?";
            }

            insertSql << ");";

            // 准备语句
            sqlite3_stmt* stmt = nullptr;
            sqlite3* db = DatabaseManager::getInstance().getDbHandle();

            if (sqlite3_prepare_v2(db, insertSql.str().c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
                setError("Failed to prepare insert statement: " + std::string(sqlite3_errmsg(db)));
                return false;
            }

            // 绑定参数
            sqlite3_bind_text(stmt, 1, taskId.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 2, timestamp.c_str(), -1, SQLITE_STATIC);

            int paramIndex = 3;
            // 首先打印所有JSON字段以便调试
            LogInfo("JSON fields in data: ");
            for (auto it = jsonData.begin(); it != jsonData.end(); ++it) {
                LogInfo(" - Field: " + it.key());
            }

            // 打印所有列名映射
            LogInfo("Column mappings for table " + tableName + ":");
            for (const auto& mapping : columnMapping) {
                LogInfo(" - Column: " + mapping.first + " -> JSON field: " + mapping.second);
            }

            for (const auto& col : columns) {
                // 在JSON中查找原始列名
                std::string jsonKey = "";
                bool keyFound = false;

                // 首先检查是否有映射关系
                auto mapIt = columnMapping.find(col.first);
                if (mapIt != columnMapping.end()) {
                    // 使用映射的原始名
                    jsonKey = mapIt->second;
                    LogInfo("Using mapped key for column " + col.first + ": " + jsonKey);

                    // 检查映射的字段是否存在
                    if (jsonData.contains(jsonKey)) {
                        keyFound = true;
                    } else {
                        LogWarning("Mapped JSON field '" + jsonKey + "' not found for column '" + col.first + "'");
                    }
                }

                // 如果没有映射或映射的字段不存在，尝试其他方法
                if (!keyFound) {
                    // 尝试使用列名本身
                    if (jsonData.contains(col.first)) {
                        jsonKey = col.first;
                        keyFound = true;
                        LogInfo("Using column name as JSON field: " + jsonKey);
                    }
                    // 如果列名以"col_"开头，可能是关键字列，尝试去除前缀
                    else if (col.first.size() > 4 && col.first.substr(0, 4) == "col_" && jsonData.contains(col.first.substr(4))) {
                        jsonKey = col.first.substr(4);
                        keyFound = true;
                        LogInfo("Using column name without 'col_' prefix: " + jsonKey);
                    }
                    // 如果列名以"n_"开头，可能是数字开头的列名，尝试去除前缀
                    else if (col.first.size() > 2 && col.first.substr(0, 2) == "n_" && jsonData.contains(col.first.substr(2))) {
                        jsonKey = col.first.substr(2);
                        keyFound = true;
                        LogInfo("Using column name without 'n_' prefix: " + jsonKey);
                    }
                    // 如果还是找不到，尝试忽略大小写的匹配
                    else {
                        std::string lowerColName = col.first;
                        std::transform(lowerColName.begin(), lowerColName.end(), lowerColName.begin(),
                                      [](unsigned char c) { return std::tolower(c); });

                        for (auto it = jsonData.begin(); it != jsonData.end(); ++it) {
                            std::string lowerJsonKey = it.key();
                            std::transform(lowerJsonKey.begin(), lowerJsonKey.end(), lowerJsonKey.begin(),
                                          [](unsigned char c) { return std::tolower(c); });

                            if (lowerColName == lowerJsonKey) {
                                jsonKey = it.key();
                                keyFound = true;
                                LogInfo("Found case-insensitive match: column '" + col.first + "' -> JSON field '" + jsonKey + "'");
                                break;
                            }
                        }
                    }
                }

                if (keyFound && jsonData.contains(jsonKey)) {
                    const auto& value = jsonData[jsonKey];
                    LogInfo("Binding value for column '" + col.first + "' from JSON field '" + jsonKey + "'");

                    if (value.is_null()) {
                        sqlite3_bind_null(stmt, paramIndex);
                    } else if (value.is_string()) {
                        // 处理字符串，确保中文以UTF-8编码存储
                        std::string strValue = value.get<std::string>();
                        // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，让SQLite复制字符串
                        sqlite3_bind_text(stmt, paramIndex, strValue.c_str(), -1, SQLITE_TRANSIENT);
                    } else if (value.is_number_integer()) {
                        sqlite3_bind_int64(stmt, paramIndex, value.get<int64_t>());
                    } else if (value.is_number_float()) {
                        sqlite3_bind_double(stmt, paramIndex, value.get<double>());
                    } else if (value.is_boolean()) {
                        sqlite3_bind_int(stmt, paramIndex, value.get<bool>() ? 1 : 0);
                    } else {
                        // 对于复杂类型，存储JSON字符串
                        std::string jsonStr = value.dump();
                        // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，让SQLite复制字符串
                        sqlite3_bind_text(stmt, paramIndex, jsonStr.c_str(), -1, SQLITE_TRANSIENT);
                    }
                } else {
                    LogWarning("No matching JSON field found for column '" + col.first + "', setting NULL");
                    sqlite3_bind_null(stmt, paramIndex);
                }

                paramIndex++;
            }

            // 执行语句
            bool success = sqlite3_step(stmt) == SQLITE_DONE;
            sqlite3_finalize(stmt);

            return success;
        } else if (jsonData.is_array()) {
            // 开始事务
            if (!DatabaseManager::getInstance().executeQuery("BEGIN TRANSACTION;")) {
                setError("Failed to begin transaction");
                return false;
            }

            bool success = true;

            // 为每个数组元素插入一行
            for (const auto& item : jsonData) {
                if (!item.is_object()) {
                    continue;
                }

                // 构建插入语句
                std::stringstream insertSql;
                insertSql << "INSERT INTO " << tableName << " (task_id, timestamp";

                for (const auto& col : columns) {
                    insertSql << ", " << col.first;
                }

                insertSql << ") VALUES (?, ?";

                for (size_t i = 0; i < columns.size(); ++i) {
                    insertSql << ", ?";
                }

                insertSql << ");";

                // 准备语句
                sqlite3_stmt* stmt = nullptr;
                sqlite3* db = DatabaseManager::getInstance().getDbHandle();

                if (sqlite3_prepare_v2(db, insertSql.str().c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
                    setError("Failed to prepare insert statement: " + std::string(sqlite3_errmsg(db)));
                    success = false;
                    break;
                }

                // 绑定参数
                sqlite3_bind_text(stmt, 1, taskId.c_str(), -1, SQLITE_STATIC);
                sqlite3_bind_text(stmt, 2, timestamp.c_str(), -1, SQLITE_STATIC);

                int paramIndex = 3;
                // 打印数组元素中的字段
                LogInfo("JSON fields in array item: ");
                for (auto it = item.begin(); it != item.end(); ++it) {
                    LogInfo(" - Field: " + it.key());
                }

                for (const auto& col : columns) {
                    // 在JSON中查找原始列名
                    std::string jsonKey = "";
                    bool keyFound = false;

                    // 首先检查是否有映射关系
                    auto mapIt = columnMapping.find(col.first);
                    if (mapIt != columnMapping.end()) {
                        // 使用映射的原始名
                        jsonKey = mapIt->second;
                        LogInfo("Using mapped key for column " + col.first + ": " + jsonKey);

                        // 检查映射的字段是否存在
                        if (item.contains(jsonKey)) {
                            keyFound = true;
                        } else {
                            LogWarning("Mapped JSON field '" + jsonKey + "' not found for column '" + col.first + "'");
                        }
                    }

                    // 如果没有映射或映射的字段不存在，尝试其他方法
                    if (!keyFound) {
                        // 尝试使用列名本身
                        if (item.contains(col.first)) {
                            jsonKey = col.first;
                            keyFound = true;
                            LogInfo("Using column name as JSON field: " + jsonKey);
                        }
                        // 如果列名以"col_"开头，可能是关键字列，尝试去除前缀
                        else if (col.first.size() > 4 && col.first.substr(0, 4) == "col_" && item.contains(col.first.substr(4))) {
                            jsonKey = col.first.substr(4);
                            keyFound = true;
                            LogInfo("Using column name without 'col_' prefix: " + jsonKey);
                        }
                        // 如果列名以"n_"开头，可能是数字开头的列名，尝试去除前缀
                        else if (col.first.size() > 2 && col.first.substr(0, 2) == "n_" && item.contains(col.first.substr(2))) {
                            jsonKey = col.first.substr(2);
                            keyFound = true;
                            LogInfo("Using column name without 'n_' prefix: " + jsonKey);
                        }
                        // 如果还是找不到，尝试忽略大小写的匹配
                        else {
                            std::string lowerColName = col.first;
                            std::transform(lowerColName.begin(), lowerColName.end(), lowerColName.begin(),
                                          [](unsigned char c) { return std::tolower(c); });

                            for (auto it = item.begin(); it != item.end(); ++it) {
                                std::string lowerJsonKey = it.key();
                                std::transform(lowerJsonKey.begin(), lowerJsonKey.end(), lowerJsonKey.begin(),
                                              [](unsigned char c) { return std::tolower(c); });

                                if (lowerColName == lowerJsonKey) {
                                    jsonKey = it.key();
                                    keyFound = true;
                                    LogInfo("Found case-insensitive match: column '" + col.first + "' -> JSON field '" + jsonKey + "'");
                                    break;
                                }
                            }
                        }
                    }

                    if (keyFound && item.contains(jsonKey)) {
                        const auto& value = item[jsonKey];
                        LogInfo("Binding value for column '" + col.first + "' from JSON field '" + jsonKey + "'");

                        if (value.is_null()) {
                            sqlite3_bind_null(stmt, paramIndex);
                        } else if (value.is_string()) {
                            // 处理字符串，确保中文以UTF-8编码存储
                            std::string strValue = value.get<std::string>();
                            // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，让SQLite复制字符串
                            sqlite3_bind_text(stmt, paramIndex, strValue.c_str(), -1, SQLITE_TRANSIENT);
                        } else if (value.is_number_integer()) {
                            sqlite3_bind_int64(stmt, paramIndex, value.get<int64_t>());
                        } else if (value.is_number_float()) {
                            sqlite3_bind_double(stmt, paramIndex, value.get<double>());
                        } else if (value.is_boolean()) {
                            sqlite3_bind_int(stmt, paramIndex, value.get<bool>() ? 1 : 0);
                        } else {
                            // 对于复杂类型，存储JSON字符串
                            std::string jsonStr = value.dump();
                            // 使用SQLITE_TRANSIENT而不是SQLITE_STATIC，让SQLite复制字符串
                            sqlite3_bind_text(stmt, paramIndex, jsonStr.c_str(), -1, SQLITE_TRANSIENT);
                        }
                    } else {
                        LogWarning("No matching JSON field found for column '" + col.first + "', setting NULL");
                        sqlite3_bind_null(stmt, paramIndex);
                    }

                    paramIndex++;
                }

                // 执行语句
                if (sqlite3_step(stmt) != SQLITE_DONE) {
                    setError("Failed to insert data: " + std::string(sqlite3_errmsg(db)));
                    success = false;
                }

                sqlite3_finalize(stmt);

                if (!success) {
                    break;
                }
            }

            // 提交或回滚事务
            if (success) {
                success = DatabaseManager::getInstance().executeQuery("COMMIT;");
            } else {
                DatabaseManager::getInstance().executeQuery("ROLLBACK;");
            }

            return success;
        } else {
            LogWarning("Unsupported JSON structure for data insertion: " + tableName);
            return false;
        }
    } catch (const std::exception& e) {
        setError("Exception during data insertion: " + std::string(e.what()));
        DatabaseManager::getInstance().executeQuery("ROLLBACK;");
        return false;
    }
}

std::string JsonSchemaProcessor::sanitizeColumnName(const std::string& columnName, bool* wasModified) {
    // SQLite关键字列表
    static const std::unordered_set<std::string> sqliteKeywords = {
        "abort", "action", "add", "after", "all", "alter", "analyze", "and", "as", "asc",
        "attach", "autoincrement", "before", "begin", "between", "by", "cascade", "case",
        "cast", "check", "collate", "column", "commit", "conflict", "constraint", "create",
        "cross", "current", "current_date", "current_time", "current_timestamp", "database",
        "default", "deferrable", "deferred", "delete", "desc", "detach", "distinct", "drop",
        "each", "else", "end", "escape", "except", "exclusive", "exists", "explain",
        "fail", "for", "foreign", "from", "full", "glob", "group", "having",
        "if", "ignore", "immediate", "in", "index", "indexed", "initially", "inner",
        "insert", "instead", "intersect", "into", "is", "isnull", "join", "key",
        "left", "like", "limit", "match", "natural", "no", "not", "notnull",
        "null", "of", "offset", "on", "or", "order", "outer", "plan",
        "pragma", "primary", "query", "raise", "recursive", "references", "regexp", "reindex",
        "release", "rename", "replace", "restrict", "right", "rollback", "row", "savepoint",
        "select", "set", "table", "temp", "temporary", "then", "to", "transaction",
        "trigger", "union", "unique", "update", "using", "vacuum", "values", "view",
        "virtual", "when", "where", "with", "without"
    };

    // 首先检查列名是否为空
    if (columnName.empty()) {
        if (wasModified) *wasModified = true;
        return "empty_column";
    }

    // 转换为小写进行检查
    std::string lowerName = columnName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(),
                  [](unsigned char c) { return std::tolower(c); });

    // 如果是关键字，添加前缀
    if (sqliteKeywords.find(lowerName) != sqliteKeywords.end()) {
        if (wasModified) *wasModified = true;
        return "col_" + columnName;
    }

    // 检查是否包含非法字符
    bool hasInvalidChar = false;
    for (char c : columnName) {
        if (!std::isalnum(c) && c != '_') {
            hasInvalidChar = true;
            break;
        }
    }

    // 如果包含非法字符，替换为下划线
    if (hasInvalidChar) {
        std::string sanitized = columnName;
        for (char& c : sanitized) {
            if (!std::isalnum(c) && c != '_') {
                c = '_';
            }
        }
        if (wasModified) *wasModified = true;
        return sanitized;
    }

    // 如果列名以数字开头，添加前缀
    if (std::isdigit(columnName[0])) {
        if (wasModified) *wasModified = true;
        return "n_" + columnName;
    }

    if (wasModified) *wasModified = false;
    return columnName;
}

std::string JsonSchemaProcessor::determineColumnType(const json& value) {
    if (value.is_null()) {
        return "TEXT";
    } else if (value.is_string()) {
        return "TEXT";
    } else if (value.is_number_integer()) {
        return "INTEGER";
    } else if (value.is_number_float()) {
        return "REAL";
    } else if (value.is_boolean()) {
        return "INTEGER"; // SQLite没有布尔类型，使用INTEGER
    } else if (value.is_object() || value.is_array()) {
        return "TEXT"; // 存储为JSON字符串
    } else {
        return "UNSUPPORTED";
    }
}

std::string JsonSchemaProcessor::generateTableName(const std::string& dllName, const std::string& funcName) {
    // 转换为小写并替换非法字符
    std::string tableName = dllName + "_" + funcName;
    std::transform(tableName.begin(), tableName.end(), tableName.begin(),
                  [](unsigned char c) { return std::tolower(c); });

    // 替换非法字符为下划线
    for (char& c : tableName) {
        if (!std::isalnum(c)) {
            c = '_';
        }
    }

    // 确保表名以字母开头
    if (!std::isalpha(tableName[0])) {
        tableName = "t_" + tableName;
    }

    return tableName;
}

bool JsonSchemaProcessor::tableExists(const std::string& tableName) {
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?;";

    sqlite3_stmt* stmt = nullptr;
    sqlite3* db = DatabaseManager::getInstance().getDbHandle();

    if (sqlite3_prepare_v2(db, sql.c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
        return false;
    }

    sqlite3_bind_text(stmt, 1, tableName.c_str(), -1, SQLITE_STATIC);

    bool exists = false;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        exists = true;
    }

    sqlite3_finalize(stmt);
    return exists;
}

std::unordered_map<std::string, std::string> JsonSchemaProcessor::getTableColumns(const std::string& tableName) {
    std::unordered_map<std::string, std::string> columns;

    std::string sql = "PRAGMA table_info(" + tableName + ");";

    sqlite3_stmt* stmt = nullptr;
    sqlite3* db = DatabaseManager::getInstance().getDbHandle();

    if (sqlite3_prepare_v2(db, sql.c_str(), -1, &stmt, nullptr) != SQLITE_OK) {
        return columns;
    }

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        std::string name = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        std::string type = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));

        // 跳过id、task_id和timestamp列
        if (name != "id" && name != "task_id" && name != "timestamp") {
            columns[name] = type;
        }
    }

    sqlite3_finalize(stmt);
    return columns;
}

bool JsonSchemaProcessor::updateTableStructure(const std::string& tableName,
                                              const std::unordered_map<std::string, std::string>& existingColumns,
                                              const std::unordered_map<std::string, std::string>& newColumns) {
    // 获取列名映射
    std::unordered_map<std::string, std::string> columnMapping;
    {
        std::lock_guard<std::mutex> lock(mappingCacheMutex_);
        auto it = columnMappingCache_.find(tableName);
        if (it != columnMappingCache_.end()) {
            columnMapping = it->second;
        }
    }
    try {
        // 查找需要添加的列
        std::vector<std::pair<std::string, std::string>> columnsToAdd;

        for (const auto& col : newColumns) {
            if (existingColumns.find(col.first) == existingColumns.end()) {
                columnsToAdd.push_back(col);
            }
        }

        // 如果没有新列，不需要更新
        if (columnsToAdd.empty()) {
            return true;
        }

        // 开始事务
        if (!DatabaseManager::getInstance().executeQuery("BEGIN TRANSACTION;")) {
            setError("Failed to begin transaction for table update");
            return false;
        }

        bool success = true;

        // 添加新列
        for (const auto& col : columnsToAdd) {
            std::stringstream alterSql;
            alterSql << "ALTER TABLE " << tableName << " ADD COLUMN "
                     << col.first << " " << col.second << ";";

            if (!DatabaseManager::getInstance().executeQuery(alterSql.str())) {
                setError("Failed to add column " + col.first + " to table " + tableName);
                success = false;
                break;
            }
        }

        // 提交或回滚事务
        if (success) {
            if (!DatabaseManager::getInstance().executeQuery("COMMIT;")) {
                setError("Failed to commit transaction for table update");
                success = false;
            }
        } else {
            DatabaseManager::getInstance().executeQuery("ROLLBACK;");
        }

        // 更新缓存
        if (success) {
            // 更新表结构缓存
            {
                std::lock_guard<std::mutex> lock(schemaCacheMutex_);
                auto& cachedColumns = tableSchemaCache_[tableName];

                // 合并现有列和新列
                for (const auto& col : existingColumns) {
                    cachedColumns[col.first] = col.second;
                }

                for (const auto& col : newColumns) {
                    cachedColumns[col.first] = col.second;
                }
            }

            // 更新列名映射缓存
            if (!columnMapping.empty()) {
                std::lock_guard<std::mutex> lock(mappingCacheMutex_);
                auto& cachedMapping = columnMappingCache_[tableName];

                // 合并映射
                for (const auto& mapping : columnMapping) {
                    cachedMapping[mapping.first] = mapping.second;
                }
            }
        }

        return success;
    } catch (const std::exception& e) {
        setError("Exception during table structure update: " + std::string(e.what()));
        DatabaseManager::getInstance().executeQuery("ROLLBACK;");
        return false;
    }
}
