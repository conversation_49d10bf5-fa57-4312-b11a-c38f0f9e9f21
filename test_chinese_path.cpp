#include <iostream>
#include <string>
#include <Windows.h>
#include "utils/Utils.h"
#include "database/DatabaseManager.h"

// 测试中文路径处理功能
int main() {
    try {
        std::cout << "=== 中文路径处理测试 ===" << std::endl;
        
        // 测试路径编码转换函数
        std::cout << "\n1. 测试路径编码转换函数:" << std::endl;
        
        // 获取当前程序路径
        std::string basePathAnsi = Utils::getBasePath();
        std::string basePathUtf8 = Utils::getBasePathUtf8();
        
        std::cout << "ANSI路径: " << basePathAnsi << std::endl;
        std::cout << "UTF-8路径: " << basePathUtf8 << std::endl;
        
        // 测试宽字符转换
        std::wstring testWideStr = L"测试中文路径\\数据库\\test.db";
        std::string utf8Str = Utils::wideToUtf8(testWideStr);
        std::wstring backToWide = Utils::utf8ToWide(utf8Str);
        
        std::cout << "UTF-8转换测试: " << utf8Str << std::endl;
        std::wcout << L"转换回宽字符: " << backToWide << std::endl;
        
        // 测试目录创建
        std::cout << "\n2. 测试目录创建功能:" << std::endl;
        std::string testDir = basePathUtf8 + "测试目录\\子目录\\test.db";
        bool dirCreated = Utils::ensureDirectoryExistsUtf8(testDir);
        std::cout << "目录创建结果: " << (dirCreated ? "成功" : "失败") << std::endl;
        
        // 测试数据库初始化
        std::cout << "\n3. 测试数据库初始化:" << std::endl;
        std::string dbPath = basePathUtf8 + "测试数据库\\tasks.db";
        std::cout << "数据库路径: " << dbPath << std::endl;
        
        bool dbInitialized = DatabaseManager::getInstance().initialize(dbPath);
        std::cout << "数据库初始化结果: " << (dbInitialized ? "成功" : "失败") << std::endl;
        
        if (!dbInitialized) {
            std::cout << "错误信息: " << DatabaseManager::getInstance().getLastError() << std::endl;
        }
        
        // 清理
        DatabaseManager::getInstance().cleanup();
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}
